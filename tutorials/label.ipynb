{"cells": [{"cell_type": "markdown", "id": "c067d88e", "metadata": {}, "source": ["## Classify Text into Labels"]}, {"cell_type": "code", "execution_count": 1, "id": "f8e021dd", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")\n", "\n", "import tool\n", "\n", "\n", "model = tool.get_model()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "de6740fa", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from pydantic import BaseModel, Field\n", "\n", "tagging_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "{input}\n", "\"\"\"\n", ")\n", "\n", "llm = model\n", "class Classification(BaseModel):\n", "    sentiment: str = Field(description=\"The sentiment of the text\")\n", "    aggressiveness: int = Field(\n", "        description=\"How aggressive the text is on a scale from 1 to 10\"\n", "    )\n", "    language: str = Field(description=\"The language the text is written in\")\n", "\n", "# Structured LLM\n", "structured_llm = llm.with_structured_output(Classification)"]}, {"cell_type": "code", "execution_count": 3, "id": "951b2e49", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\n", "\n"]}, {"data": {"text/plain": ["Classification(sentiment='positive', aggressiveness=0, language='es')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "\n", "print(prompt.messages[0].content)\n", "response = structured_llm.invoke(prompt)\n", "\n", "response"]}, {"cell_type": "code", "execution_count": 4, "id": "45632e29", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sentiment': 'negative', 'aggressiveness': 100, 'language': 'es'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# output json\n", "inp = \"Estoy muy enojado con vos! Te voy a dar tu merecido!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "response = structured_llm.invoke(prompt)\n", "\n", "\n", "response.model_dump()"]}, {"cell_type": "markdown", "id": "194c29b6", "metadata": {}, "source": ["### 精细化控制输出"]}, {"cell_type": "code", "execution_count": 5, "id": "bb852177", "metadata": {}, "outputs": [], "source": ["class Classification(BaseModel):\n", "    sentiment: str = Field(\n", "        description=\"The sentiment of the text\", enum=[\"happy\", \"neutral\", \"sad\"]\n", "    )\n", "    aggressiveness: int = Field(\n", "        description=\"How aggressive the text is on a scale from 1 to 10\",\n", "        enum=[1, 2, 3, 4, 5],\n", "    )\n", "    language: str = Field(\n", "        description=\"The language the text is written in\",\n", "        enum=[\"spanish\", \"english\", \"french\", \"german\", \"italian\"],\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "id": "4033c55c", "metadata": {}, "outputs": [], "source": ["tagging_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "{input}\n", "\"\"\"\n", ")\n", "\n", "llm = tool.get_openai(temperature=0).with_structured_output(Classification)"]}, {"cell_type": "code", "execution_count": 8, "id": "ed19c3e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["Classification(sentiment='happy', aggressiveness=1, language='spanish')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "llm.invoke(prompt)"]}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}