from langchain.chat_models import init_chat_model

import tool


model = tool.get_model()
# result = model.invoke("hello")
# print(result.content)


# from langchain_core.messages import HumanMessage, SystemMessage
# messages = [
#     SystemMessage("Translate the following from English to Chinese"),
#     HumanMessage("Hi!")
# ]
# result = model.invoke(messages)
# print(result.content)


### using prompt template
from langchain_core.prompts import ChatPromptTemplate

system_template = "Translate the following from English to {language}"
prompt_template = ChatPromptTemplate.from_messages(
    [("system", system_template), ("user", "{text}")]
)

prompt = prompt_template.invoke({"text": "hi", "language": "Chinese"})
result = model.invoke(prompt.to_messages())
print(result.content)
