import os

import dotenv
import getpass
from langchain.chat_models import init_chat_model
from langchain_openai import OpenAIEmbeddings
from pydantic import SecretStr


dotenv.load_dotenv()

DEEPSEEK_BASE_URL = "https://api.siliconflow.cn/v1"

BASE_URL = "https://openrouter.ai/api/v1/"


EMBEDDING_URL = ""


def get_model():
    if os.environ.get("SILICONFLOW_API_KEY") is None:
        os.environ["SILICONFLOW_API_KEY"] = getpass.getpass("Enter your api key:")

    model = init_chat_model(
        "deepseek-ai/DeepSeek-V3",
        model_provider="openai",
        base_url=DEEPSEEK_BASE_URL,
        api_key=os.environ["SILICONFLOW_API_KEY"],
    )

    return model


def get_embedding_model():
    from langchain_community.embeddings import HunyuanEmbeddings

    embeddings = HunyuanEmbeddings(
        region="ap-guangzhou",
        hunyuan_secret_id=SecretStr("AKIDtqgXIqFYj4PZaTFrIYdXHo6HJCvWyHQO"),
        hunyuan_secret_key=SecretStr("sJbkjXAj6HFC2e6OellqxynSyIf2e7OS"),
    )
    return embeddings


def get_openai(model="deepseek-ai/DeepSeek-V3", temperature: float | None = None):
    temperature_value = 0.9
    if temperature is not None:
        temperature_value = temperature

    llm = init_chat_model(
        temperature=temperature_value,
        max_tokens=500,
        base_url=DEEPSEEK_BASE_URL,
        model_provider="openai",
        model=model,
    )
    return llm


def get_openrouter(
    model="deepseek/deepseek-chat-v3-0324:free", temperature: float | None = None
):
    temperature_value = 0.9
    if temperature is not None:
        temperature_value = temperature

    llm = init_chat_model(
        temperature=temperature_value,
        max_tokens=500,
        base_url=BASE_URL,
        model_provider="openai",
        model=model,
    )
    return llm
