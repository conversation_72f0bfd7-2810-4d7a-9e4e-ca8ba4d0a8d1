{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1b33cc09", "metadata": {}, "outputs": [], "source": ["import tool\n", "\n", "llm = tool.get_model()"]}, {"cell_type": "markdown", "id": "98fdc317", "metadata": {}, "source": ["### structured output by pydantic object."]}, {"cell_type": "code", "execution_count": 2, "id": "a1a3286b", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class ResponsdFormatter(BaseModel):\n", "    \"\"\"Alawys use this tool to structure your response to the user.\"\"\"\n", "    answer: str = Field(description=\"The answer to the user's question\")\n", "    followup_question: str = Field(description=\"A followup question the user could ask\")"]}, {"cell_type": "code", "execution_count": 7, "id": "7303a7d4", "metadata": {}, "outputs": [], "source": ["structured_output_llm = llm.with_structured_output(ResponsdFormatter)\n", "result = structured_output_llm.invoke(\"What's is the powerhouse of the cell?\")"]}, {"cell_type": "code", "execution_count": 8, "id": "2c152306", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"answer\":\"The powerhouse of the cell is the **mitochondrion** (plural: mitochondria).\",\"followup_question\":\"Why is the mitochondrion called the powerhouse of the cell? **Explain in simple terms.**\"}'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result.model_dump_json()"]}, {"cell_type": "code", "execution_count": 9, "id": "f88e0f01", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answer': 'The powerhouse of the cell is the **mitochondrion** (plural: mitochondria).',\n", " 'followup_question': 'Why is the mitochondrion called the powerhouse of the cell? **Explain in simple terms.**'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result.model_dump()"]}, {"cell_type": "markdown", "id": "7d3829f5", "metadata": {}, "source": ["### structured output by json object."]}, {"cell_type": "code", "execution_count": 20, "id": "f6cf5f64", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answer': \"The powerhouse of the cell is the **mitochondrion** (plural: mitochondria). It is often referred to by this nickname because it generates most of the cell's supply of **adenosine triphosphate (ATP)**, the molecule that provides energy for cellular processes.\"}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["format = schema = {\n", "    \"title\": \"responsd_formatter\",\n", "    \"description\": \"Alawys use this tool to structure your response to the user\",\n", "    \"type\": \"object\",\n", "    \"properties\": {\n", "        \"answer\": {\"type\": \"string\", \"description\": \"The answer to the user's question\"},\n", "        \"followup_question\": {\"type\": \"string\", \"description\": \"A followup question the user could ask\"}\n", "    },\n", "    \"required\": [\"answer\", \"folloup_question\"]\n", "}\n", "\n", "structured_output_llm = llm.with_structured_output(format)\n", "structured_output_llm.invoke(\"What's the powerhouse of the cell?\")"]}, {"cell_type": "markdown", "id": "3b5d2074", "metadata": {}, "source": ["### json_mode"]}, {"cell_type": "code", "execution_count": null, "id": "31dbd6dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'random_ints': [31, 52, 14, 87, 5, 72, 43, 98, 26, 67]}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_output_llm = llm.with_structured_output(method=\"json_mode\") # PS: 为什么不传schema不报错呢？\n", "structured_output_llm.invoke(\"Return a JSON object with key 'random_ints' and a value of 10 random ints in [0-99]\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "79c7f0a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method ChatOpenAI.with_structured_output of ChatOpenAI(client=<openai.resources.chat.completions.completions.Completions object at 0x1061778c0>, async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x106241250>, root_client=<openai.OpenAI object at 0x103dd42c0>, root_async_client=<openai.AsyncOpenAI object at 0x105869130>, model_name='deepseek-ai/DeepSeek-V3', model_kwargs={}, openai_api_key=SecretStr('**********'), openai_api_base='https://api.siliconflow.cn/v1')>\n"]}], "source": ["print(llm.with_structured_output) # ChatOpenAI重写了with_structed_output方法，给了 schema 默认值 None\n"]}, {"cell_type": "code", "execution_count": null, "id": "9107cf04", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}