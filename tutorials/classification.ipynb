{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e48924da", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")\n", "\n", "import tool\n", "\n", "llm = tool.get_model()"]}, {"cell_type": "code", "execution_count": 2, "id": "2153320f", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "tagging_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"\n", "Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "{input}\n", "\"\"\"\n", ")\n", "\n", "class Classification(BaseModel):\n", "    sentiment: str = Field(description=\"The sentiment of the text\")\n", "    aggressiveness: int = Field(\n", "        description=\"How aggressive the text is ont a scale from 1 to 10\"\n", "    )\n", "    language: str = Field(description=\"The language the text is written in\")\n", "\n", "structed_llm = llm.with_structured_output(Classification)"]}, {"cell_type": "code", "execution_count": 3, "id": "d48f7096", "metadata": {}, "outputs": [{"data": {"text/plain": ["Classification(sentiment='positive', aggressiveness=0, language='es')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "response = structed_llm.invoke(prompt)\n", "\n", "response"]}, {"cell_type": "markdown", "id": "284b7b51", "metadata": {}, "source": ["### Finer control"]}, {"cell_type": "code", "execution_count": 4, "id": "c1874205", "metadata": {}, "outputs": [], "source": ["class Classification(BaseModel):\n", "    sentiment: str = Field(\n", "        description=\"The sentiment of the text\", enum=[\"happy\", \"neutral\", \"sad\"]\n", "    )\n", "    aggressiveness: int = Field(\n", "        description=\"How aggressive the text is ont a scale from 1 to 10\",\n", "        enum=[1, 2, 3, 4, 5],\n", "    )\n", "    language: str = Field(\n", "        description=\"The language the text is written in\",\n", "        enum=[\"spanish\", \"english\", \"french\", \"german\", \"italian\"]\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "id": "3cdfff6d", "metadata": {}, "outputs": [], "source": ["structed_llm = llm.with_structured_output(Classification)"]}, {"cell_type": "code", "execution_count": 6, "id": "77aaf9bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Classification(sentiment='happy', aggressiveness=1, language='spanish')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "structed_llm.invoke(prompt)"]}, {"cell_type": "code", "execution_count": 9, "id": "c2257de0", "metadata": {}, "outputs": [{"data": {"text/plain": ["Classification(sentiment='neutral', aggressiveness=1, language='spanish')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Estoy muy enojado con vos! Te voy a dar tu merecido!\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "structed_llm.invoke(prompt)"]}, {"cell_type": "code", "execution_count": 10, "id": "c5c3da09", "metadata": {}, "outputs": [{"data": {"text/plain": ["Classification(sentiment='neutral', aggressiveness=3, language='english')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["inp = \"Weather is ok here, I can go outside without much more than a coat\"\n", "prompt = tagging_prompt.invoke({\"input\": inp})\n", "structed_llm.invoke(prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "114a0794", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}