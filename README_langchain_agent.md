# LangChain Agent 实现 - 让大模型拥有双手

这是基于 [CloudWeGo Eino 文档](https://www.cloudwego.io/zh/docs/eino/quick_start/agent_llm_with_tools/) 的 LangChain 等价实现。

## 📋 概述

本项目展示了如何使用 LangChain 构建一个具有工具调用能力的 Agent，实现了与 Eino 框架相同的功能，包括：

- 🛠️ 多种工具实现方式
- 🤖 完整的 Agent 构建流程
- 🔄 ReAct Agent 模式
- 🤝 Multi Agent 协作
- 📝 待办事项管理系统

## 🏗️ 架构对比

| 功能 | Eino 实现 | LangChain 实现 |
|------|-----------|----------------|
| **ChatModel** | `openai.NewChatModel` | `tool.get_model()` |
| **Tool 创建方式一** | `utils.NewTool` | `@langchain_tool` 装饰器 |
| **Tool 创建方式二** | `utils.InferTool` | `StructuredTool.from_function` |
| **Tool 创建方式三** | 实现 `Tool` 接口 | 继承 `BaseTool` 类 |
| **Tool 创建方式四** | 官方封装工具 | `DuckDuckGoSearchResults` 等 |
| **Agent 构建** | `compose.NewChain` | `create_tool_calling_agent` |
| **ReAct Agent** | Eino ReAct Agent | `create_react_agent` |
| **Multi Agent** | Eino Multi Agent | 自定义协调器 |

## 🛠️ 工具实现方式

### 方式一：使用装饰器（@langchain_tool）

```python
@langchain_tool
def add_todo_tool(params: TodoAddParams) -> str:
    """添加一个待办事项"""
    # 实现逻辑
    return json.dumps(result)
```

**优点**: 简洁直观，类似函数定义
**缺点**: 需要单独定义参数类

### 方式二：使用 StructuredTool

```python
update_todo_tool = StructuredTool.from_function(
    name="update_todo",
    description="更新待办事项",
    func=update_todo_func
)
```

**优点**: 灵活性高，可以复用现有函数
**缺点**: 需要额外的配置代码

### 方式三：继承 BaseTool 类

```python
class ListTodoTool(BaseTool):
    name: str = "list_todo"
    description: str = "列出所有待办事项"
    
    def _run(self, **kwargs) -> str:
        # 实现逻辑
        return result
```

**优点**: 最大的自定义能力，支持复杂逻辑
**缺点**: 代码量较多

### 方式四：使用官方工具

```python
search_tool = DuckDuckGoSearchResults(
    output_format="json",
    num_results=5
)
```

**优点**: 开箱即用，稳定可靠
**缺点**: 定制化程度有限

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install langchain langchain-community langchain-core pydantic
```

### 2. 配置环境

确保你的 `tool.py` 文件中有正确的模型配置。

### 3. 运行基础示例

```bash
python langchain_agent_with_tools.py
```

### 4. 运行对比演示

```bash
python langchain_agent_with_tools.py comparison
```

## 📊 功能特性

### 🎯 核心功能

- ✅ **添加待办事项**: 支持内容、开始时间、截止时间
- ✅ **更新待办事项**: 支持修改任意字段
- ✅ **列出待办事项**: 支持按完成状态过滤
- ✅ **搜索功能**: 集成 DuckDuckGo 搜索

### 🔧 Agent 类型

1. **标准 Tool Calling Agent**
   - 使用 `create_tool_calling_agent`
   - 支持多工具调用
   - 自动工具选择

2. **ReAct Agent**
   - 思考-行动-观察循环
   - 适合复杂推理任务
   - 可观察的决策过程

3. **Multi Agent 协作**
   - 专门化的 Agent
   - 智能路由机制
   - 协调器模式

## 📝 使用示例

### 基础用法

```python
from langchain_agent_with_tools import create_todo_agent

# 创建 Agent
agent = create_todo_agent()

# 执行任务
response = agent.invoke({
    "input": "添加一个学习 LangChain 的待办事项"
})

print(response["output"])
```

### ReAct Agent

```python
from langchain_agent_with_tools import demo_react_agent

# 创建 ReAct Agent
react_agent = demo_react_agent()

# 执行复杂推理任务
response = react_agent.invoke({
    "input": "搜索 LangChain 的最新功能，然后添加相关的学习任务"
})
```

### Multi Agent 协作

```python
from langchain_agent_with_tools import demo_multi_agent

# 创建协调器
coordinator = demo_multi_agent()

# 自动路由到合适的 Agent
result = coordinator("搜索人工智能的最新发展")
```

## 🔍 与 Eino 的对比

### 相似之处

1. **工具抽象**: 都提供了多种工具创建方式
2. **Agent 构建**: 都支持链式构建和组合
3. **流式处理**: 都支持流式输出
4. **扩展性**: 都有良好的扩展机制

### 差异之处

1. **语言**: Eino 使用 Go，LangChain 使用 Python
2. **生态**: LangChain 生态更成熟，工具更丰富
3. **性能**: Go 在并发和性能方面有优势
4. **学习曲线**: LangChain 对 Python 开发者更友好

## 🎯 最佳实践

### 1. 工具设计

- 保持工具功能单一且明确
- 提供清晰的参数描述
- 处理异常情况并返回有意义的错误信息

### 2. Agent 配置

- 使用合适的 prompt 模板
- 设置合理的错误处理机制
- 启用 verbose 模式便于调试

### 3. 性能优化

- 缓存常用的工具结果
- 使用异步调用提高并发性能
- 合理设置超时时间

## 🔧 扩展指南

### 添加新工具

1. 选择合适的实现方式
2. 定义清晰的参数结构
3. 实现核心逻辑
4. 添加到工具列表

### 自定义 Agent

1. 创建专门的 prompt 模板
2. 选择合适的工具集合
3. 配置执行参数
4. 测试和优化

## 📚 参考资料

- [CloudWeGo Eino 文档](https://www.cloudwego.io/zh/docs/eino/)
- [LangChain 官方文档](https://python.langchain.com/)
- [Agent 设计模式](https://python.langchain.com/docs/concepts/agents/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个实现！

## 📄 许可证

MIT License
