{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b6e8b02a", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"../..\")\n", "\n", "import tool\n", "\n", "model = tool.get_model()"]}, {"cell_type": "code", "execution_count": 2, "id": "95e59e3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hi <PERSON>! 😊 Nice to meet you! How can I assist you today? 🤔', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 19, 'prompt_tokens': 8, 'total_tokens': 27, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623c4ae6139c0ebb511c49f54d0', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--0dda3b1e-64a1-4320-8870-de72110a15a2-0', usage_metadata={'input_tokens': 8, 'output_tokens': 19, 'total_tokens': 27, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "model.invoke([<PERSON><PERSON><PERSON><PERSON>(\"Hi!, I'm <PERSON>\")])\n"]}, {"cell_type": "code", "execution_count": 3, "id": "61690def", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"I don’t have access to personal information unless you share it with me. So, to answer your question: I don’t know your name yet!  \\n\\nBut you can tell me, and if you'd like, I can refer to you by name in our conversations. 😊 What should I call you?\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 64, 'prompt_tokens': 8, 'total_tokens': 72, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623caf7cd351ba8a72a7da2e199', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--471691a5-6d93-4313-a655-a3cfac019bf0-0', usage_metadata={'input_tokens': 8, 'output_tokens': 64, 'total_tokens': 72, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke([HumanMessage(\"What's my name?\")]) # 可以看到，在前一步虽然告知了我叫 Bob，但是这里大模型依然不知道我是谁"]}, {"cell_type": "markdown", "id": "bcd6558f", "metadata": {}, "source": ["### use entire conversation history to let llm remember who am i"]}, {"cell_type": "code", "execution_count": 4, "id": "e29cad0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Your name is **<PERSON>**—you mentioned it at the beginning of our conversation! 😊  \\n\\nIs there something specific you'd like help with, <PERSON>?\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 32, 'prompt_tokens': 28, 'total_tokens': 60, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623d80f37a311c1a1376835ec60', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--c93cb434-c510-4fa2-8d44-5455059cb69b-0', usage_metadata={'input_tokens': 28, 'output_tokens': 32, 'total_tokens': 60, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "messages = [\n", "    HumanMessage(\"<PERSON>, I'm <PERSON><PERSON>\"),\n", "    AIMessage(\"Hello, <PERSON>. What can i assist you today?\"),\n", "    HumanMessage(\"What's my name?\"),\n", "]\n", "\n", "model.invoke(messages)"]}, {"cell_type": "markdown", "id": "ea1de42e", "metadata": {}, "source": ["### use langgraph to a better implement."]}, {"cell_type": "code", "execution_count": 5, "id": "b3163cf9", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, MessagesState, START\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "workflow = StateGraph(MessagesState)\n", "\n", "def call_model(state: MessagesState):\n", "    response = model.invoke(state[\"messages\"])\n", "    return {\"messages\": response}\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "compiled_workflow = workflow.compile(MemorySaver())"]}, {"cell_type": "code", "execution_count": 6, "id": "83f558e8", "metadata": {}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"abc_123\"}}"]}, {"cell_type": "code", "execution_count": 7, "id": "4bb2affe", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"<PERSON>, <PERSON>' <PERSON>.\", additional_kwargs={}, response_metadata={}, id='09a012ee-19c7-4dcb-bc77-79a942facb86'),\n", "  AIMessage(content=\"Hi, <PERSON>! 👋 How's it going? What can I do for you today? 😊\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 21, 'prompt_tokens': 9, 'total_tokens': 30, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623e0282a6102c4030ac140c2fb', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--6cdebb2c-9672-4d4d-a27b-d1b3f0b21b20-0', usage_metadata={'input_tokens': 9, 'output_tokens': 21, 'total_tokens': 30, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"<PERSON>, <PERSON><PERSON> <PERSON>.\"\n", "\n", "compiled_workflow.invoke({\"messages\": [query]}, config=config)"]}, {"cell_type": "code", "execution_count": 8, "id": "cf138735", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"<PERSON>, <PERSON>' <PERSON>.\", additional_kwargs={}, response_metadata={}, id='09a012ee-19c7-4dcb-bc77-79a942facb86'),\n", "  AIMessage(content=\"Hi, <PERSON>! 👋 How's it going? What can I do for you today? 😊\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 21, 'prompt_tokens': 9, 'total_tokens': 30, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623e0282a6102c4030ac140c2fb', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--6cdebb2c-9672-4d4d-a27b-d1b3f0b21b20-0', usage_metadata={'input_tokens': 9, 'output_tokens': 21, 'total_tokens': 30, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='42ed640f-a9c6-4487-ad72-09f8fc2e847a'),\n", "  AIMessage(content='You told me your name is **Bob**! 😊  \\n\\nDid you want to change it or ask something else? Let me know how I can help!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 32, 'prompt_tokens': 38, 'total_tokens': 70, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623e818e2c8b35fb61da8547917', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--91856248-b700-42b1-9c42-a1454ef32af7-0', usage_metadata={'input_tokens': 38, 'output_tokens': 32, 'total_tokens': 70, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"What's my name?\"\n", "\n", "input_messages = [HumanMessage(query)]\n", "compiled_workflow.invoke({\"messages\": input_messages}, config=config)"]}, {"cell_type": "code", "execution_count": 9, "id": "dbee2a73", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='3d76730a-110e-4482-953d-541c7fc5b975'),\n", "  AIMessage(content='I don’t have access to personal information unless you share it with me. If you tell me your name, I’d be happy to use it in our conversation! 😊  \\n\\nAlternatively, if you’re asking about something else (like a username or context I might have missed), feel free to clarify!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 64, 'prompt_tokens': 8, 'total_tokens': 72, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623eff3ab8966b660678f4f75a6', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--f0a081ea-42c3-42fe-8960-37686d8a5b2f-0', usage_metadata={'input_tokens': 8, 'output_tokens': 64, 'total_tokens': 72, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc234\"}} # 换了thread_id, 所以大模型就不知道我是谁了\n", "\n", "input_messages = [HumanMessage(query)]\n", "compiled_workflow.invoke({\"messages\": input_messages}, config=config)"]}, {"cell_type": "code", "execution_count": 10, "id": "904f9a25", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"<PERSON>, <PERSON>' <PERSON>.\", additional_kwargs={}, response_metadata={}, id='09a012ee-19c7-4dcb-bc77-79a942facb86'),\n", "  AIMessage(content=\"Hi, <PERSON>! 👋 How's it going? What can I do for you today? 😊\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 21, 'prompt_tokens': 9, 'total_tokens': 30, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623e0282a6102c4030ac140c2fb', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--6cdebb2c-9672-4d4d-a27b-d1b3f0b21b20-0', usage_metadata={'input_tokens': 9, 'output_tokens': 21, 'total_tokens': 30, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='42ed640f-a9c6-4487-ad72-09f8fc2e847a'),\n", "  AIMessage(content='You told me your name is **Bob**! 😊  \\n\\nDid you want to change it or ask something else? Let me know how I can help!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 32, 'prompt_tokens': 38, 'total_tokens': 70, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623e818e2c8b35fb61da8547917', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--91856248-b700-42b1-9c42-a1454ef32af7-0', usage_metadata={'input_tokens': 38, 'output_tokens': 32, 'total_tokens': 70, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='cf5acc75-6630-4883-956d-bb1658dda7f5'),\n", "  AIMessage(content=\"Your name is **Bob**—unless you'd like me to call you something else! 😊 Let me know if you're teasing or if I should update your name. Either way, I'm happy to help!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 44, 'prompt_tokens': 78, 'total_tokens': 122, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b623fcc1a8b1e48a0bdffba1edbf', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--05518e23-edec-4a69-b0b8-a54457e49e35-0', usage_metadata={'input_tokens': 78, 'output_tokens': 44, 'total_tokens': 122, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc_123\"}} # 换回了之前的thread_id，大模型应该还记得我\n", "\n", "input_messages = [HumanMessage(query)]\n", "compiled_workflow.invoke({\"messages\": input_messages}, config=config)"]}, {"cell_type": "markdown", "id": "a2ad7cd0", "metadata": {}, "source": ["### Prompt tempalte "]}, {"cell_type": "code", "execution_count": 11, "id": "85564253", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "prompt_template = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You talk like a pirate. Answer all questions to the best of your ability.\",\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "f4b558ad", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(MessagesState)\n", "\n", "def call_model(state: MessagesState):\n", "    prompt = prompt_template.invoke(state) # state有个messages的属性，刚好对应prompt_template里的messages\n", "    response = model.invoke(prompt)\n", "    return {\"messages\": response}\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "compiled_workflow = workflow.compile(MemorySaver())"]}, {"cell_type": "code", "execution_count": 13, "id": "b0ca019c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"I'm <PERSON>.\", additional_kwargs={}, response_metadata={}, id='e6d97837-2bb8-4db0-bbd5-d7f343c281b5'),\n", "  AIMessage(content='*Adjusts eyepatch and squints* \\n\\nAhoy there, <PERSON>! What brings ya to these salty shores? Be ye lookin\\' for treasure, a strong grog, or just fancy a friendly chat with a scallywag like me? \\n\\n*Leans in closer* \\n\\nSpeak up, lad - me hearin\\' ain\\'t what it used ta be after all them cannon blasts! \\n\\n**For a proper pirate greeting, ye might say:**\\n_\"Arrr, Jim be yer name? Well batten down the hatches, \\'cause this here parrot\\'s got questions! What kind o\\' ship do ye sail on, <PERSON>?\"_\\n\\n*Parrot squawks in background* \\n\"Pieces o\\' eight! Pieces o\\' eight!\" \\n\\nHow\\'s that for pirate talk, eh? Want me ta keep it up or scale it back a bit? I can go full Blackbeard or just sprinkle in a few \"arrrs\" here and there! \\n\\nEither way, <PERSON>, ye\\'ve got me attention - so ask away or tell me more! The sea\\'s wide open (and so\\'s me rum barrel, if ye catch me drift!)', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 240, 'prompt_tokens': 23, 'total_tokens': 263, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b624064669ebcda4c8cf8392e55a', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--5f9a19e7-1c00-402f-952d-809d7a6f424f-0', usage_metadata={'input_tokens': 23, 'output_tokens': 240, 'total_tokens': 263, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc___\"}}\n", "\n", "compiled_workflow.invoke({\"messages\": [HumanMessage(\"I'm <PERSON>.\")]}, config=config)"]}, {"cell_type": "code", "execution_count": 14, "id": "fa59b236", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"I'm <PERSON>.\", additional_kwargs={}, response_metadata={}, id='e6d97837-2bb8-4db0-bbd5-d7f343c281b5'),\n", "  AIMessage(content='*Adjusts eyepatch and squints* \\n\\nAhoy there, <PERSON>! What brings ya to these salty shores? Be ye lookin\\' for treasure, a strong grog, or just fancy a friendly chat with a scallywag like me? \\n\\n*Leans in closer* \\n\\nSpeak up, lad - me hearin\\' ain\\'t what it used ta be after all them cannon blasts! \\n\\n**For a proper pirate greeting, ye might say:**\\n_\"Arrr, Jim be yer name? Well batten down the hatches, \\'cause this here parrot\\'s got questions! What kind o\\' ship do ye sail on, <PERSON>?\"_\\n\\n*Parrot squawks in background* \\n\"Pieces o\\' eight! Pieces o\\' eight!\" \\n\\nHow\\'s that for pirate talk, eh? Want me ta keep it up or scale it back a bit? I can go full Blackbeard or just sprinkle in a few \"arrrs\" here and there! \\n\\nEither way, <PERSON>, ye\\'ve got me attention - so ask away or tell me more! The sea\\'s wide open (and so\\'s me rum barrel, if ye catch me drift!)', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 240, 'prompt_tokens': 23, 'total_tokens': 263, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b624064669ebcda4c8cf8392e55a', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--5f9a19e7-1c00-402f-952d-809d7a6f424f-0', usage_metadata={'input_tokens': 23, 'output_tokens': 240, 'total_tokens': 263, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='d2a869b5-39a4-4ff3-aeb9-b8522c272f51'),\n", "  AIMessage(content='*Spits out a swig of rum in surprise*  \\n\\n**\"BLIMEY!** Are ye tryin’ to test me memory after all that grog, <PERSON>?! Yer name’s as clear as the <PERSON>lly <PERSON> on a windy day—it’s **J<PERSON>!**  \\n\\n*Pats ye hard on the back*  \\n\\nAnd if I ever forget it, me ship’s parrot’ll squawk it in me ear! **\"JIIIIM! JIIIIM!\"** *Screeches like a poorly oiled hinge*  \\n\\nBut tell me true, matey—did ye *want* me ta call ye somethin’ else? \"Captain <PERSON>\"? \"Mad-Eyed <PERSON> the Squid Whisperer\"? Or—*gasps*—are ye secretly a scoundrel with *two names* like some fancy-pants nobleman?  \\n\\n*Leans in, suspicious but grinning*  \\n**\"Spill the beans (or the rum), <PERSON>!\"**  \\n\\n---\\n\\nHow’s *this* fer pirate talk, eh? Proper theatrical or should I ease off the plank a wee bit? 😉', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 239, 'prompt_tokens': 271, 'total_tokens': 510, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b6242f8952960a22049ee21b5f2d', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--992ee1be-deef-48cf-a5d8-f0d02beef531-0', usage_metadata={'input_tokens': 271, 'output_tokens': 239, 'total_tokens': 510, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_workflow.invoke({\"messages\": [HumanMessage(\"What's my name?\")]}, config=config)"]}, {"cell_type": "markdown", "id": "b3ca3772", "metadata": {}, "source": ["### make prompt a little bit more complicated."]}, {"cell_type": "code", "execution_count": 15, "id": "a470cd28", "metadata": {}, "outputs": [], "source": ["prompt_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful assistant. Answer all questions to the best of your ability in {language}\"),\n", "    MessagesPlaceholder(variable_name=\"messages\")\n", "])"]}, {"cell_type": "code", "execution_count": 16, "id": "392799f7", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, Annotated, Sequence\n", "\n", "from pydantic import BaseModel\n", "from langchain_core.messages import BaseMessage\n", "from langgraph.graph import StateGraph, add_messages\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "\n", "class State(TypedDict): # Now, we use State instead of MessageState.\n", "    language: str\n", "    messages: Annotated[Sequence[BaseMessage], add_messages]\n", "\n", "workflow = StateGraph(State)\n", "\n", "\n", "def call_model(state: State):\n", "    prompt = prompt_template.invoke(state)\n", "    response = model.invoke(prompt)\n", "    return {\"messages\": response}\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "compiled_workflow = workflow.compile(MemorySaver())"]}, {"cell_type": "code", "execution_count": 17, "id": "32f088d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'language': 'Chinese',\n", " 'messages': [HumanMessage(content=\"<PERSON>, I'm <PERSON><PERSON>\", additional_kwargs={}, response_metadata={}, id='40ebf70c-bcee-4483-935a-78be744015db'),\n", "  AIMessage(content='你好，Bob！很高兴认识你！😊 我是你的智能助手，有什么可以帮你的吗？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 22, 'prompt_tokens': 26, 'total_tokens': 48, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b624560315a39b3ce0005c866369', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--c6354629-7874-4985-8015-b7f4658e0964-0', usage_metadata={'input_tokens': 26, 'output_tokens': 22, 'total_tokens': 48, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"thread_id\": \"asasd\"}}\n", "compiled_workflow.invoke({\"messages\": \"<PERSON>, I'm <PERSON><PERSON>\", \"language\": \"Chinese\"}, config=config)"]}, {"cell_type": "code", "execution_count": 18, "id": "0b135566", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'language': 'Chinese',\n", " 'messages': [HumanMessage(content=\"<PERSON>, I'm <PERSON><PERSON>\", additional_kwargs={}, response_metadata={}, id='40ebf70c-bcee-4483-935a-78be744015db'),\n", "  AIMessage(content='你好，Bob！很高兴认识你！😊 我是你的智能助手，有什么可以帮你的吗？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 22, 'prompt_tokens': 26, 'total_tokens': 48, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b624560315a39b3ce0005c866369', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--c6354629-7874-4985-8015-b7f4658e0964-0', usage_metadata={'input_tokens': 26, 'output_tokens': 22, 'total_tokens': 48, 'input_token_details': {}, 'output_token_details': {}}),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='931c7ba1-3d51-40ef-b3d0-0a562187310d'),\n", "  AIMessage(content='哈哈，你刚刚告诉我啦！你的名字是 **Bob**～ 😄 需要我帮你记住什么其他信息吗？还是现在有什么可以为你效劳的？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 35, 'prompt_tokens': 56, 'total_tokens': 91, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b6245c51364b7cd05bb3a3811d80', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--50367ef6-a505-4eaf-a203-ba3dc0ac4920-0', usage_metadata={'input_tokens': 56, 'output_tokens': 35, 'total_tokens': 91, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["compiled_workflow.invoke({\"messages\": \"What's my name?\"}, config=config) # now, llm also remember who am i, and we do not need give the language parameter."]}, {"cell_type": "markdown", "id": "4a393972", "metadata": {}, "source": ["### Managing Conversation History"]}, {"cell_type": "code", "execution_count": 19, "id": "4ff172c0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Projects/learn-langchain/.venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/plain": ["[HumanMessage(content='whats 2 + 2', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='4', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='thanks', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='no problem!', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='having fun?', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='yes!', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import SystemMessage, trim_messages\n", "\n", "import transformers\n", "\n", "chat_tokenizer_dir = \"/Users/<USER>/Projects/learn-langchain/deepseek_v3_tokenizer\"\n", "tokenizer = transformers.AutoTokenizer.from_pretrained(\n", "    chat_tokenizer_dir, trust_remote_code=True\n", ")\n", "def estimate_tokens(messages):\n", "    text = \"\\n\".join(f\"{m.type}: {m.content}\" for m in messages)\n", "    return len(tokenizer.encode(text))\n", "\n", "\n", "trimmer = trim_messages(\n", "    max_tokens=42,\n", "    strategy=\"last\",\n", "    token_counter=estimate_tokens,\n", "    allow_partial=False,\n", "    start_on=\"human\",\n", ")\n", "\n", "messages = [\n", "    SystemMessage(content=\"you're a good assistant\"),\n", "    HumanMessage(content=\"hi! I'm bob\"),\n", "    AIMessage(content=\"hi!\"),\n", "    HumanMessage(content=\"I like vanilla ice cream\"),\n", "    AIMessage(content=\"nice\"),\n", "    HumanMessage(content=\"whats 2 + 2\"),\n", "    AIMessage(content=\"4\"),\n", "    HumanMessage(content=\"thanks\"),\n", "    AIMessage(content=\"no problem!\"),\n", "    HumanMessage(content=\"having fun?\"),\n", "    AIMessage(content=\"yes!\"),\n", "]\n", "\n", "trimmer.invoke(messages)\n"]}, {"cell_type": "code", "execution_count": 20, "id": "60f002b2", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(state_schema=State)\n", "\n", "\n", "def call_model(state: State):\n", "    trimmed_messages = trimmer.invoke(state[\"messages\"])\n", "    prompt = prompt_template.invoke(\n", "        {\"messages\": trimmed_messages, \"language\": state[\"language\"]}\n", "    )\n", "    response = model.invoke(prompt)\n", "    return {\"messages\": [response]}\n", "\n", "\n", "workflow.add_edge(START, \"model\")\n", "workflow.add_node(\"model\", call_model)\n", "\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 21, "id": "fd43a67f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "You haven't told me your name, so I don’t know it! Feel free to introduce yourself—I’d love to know. 😊\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc567\"}}\n", "query = \"What is my name?\"\n", "language = \"English\"\n", "\n", "input_messages = messages + [HumanMessage(query)]\n", "output = app.invoke(\n", "    {\"messages\": input_messages, \"language\": language},\n", "    config,\n", ")\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 22, "id": "cde9cd5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "You asked:  \n", "\n", "**\"What's 2 + 2?\"**  \n", "\n", "...and (just in case you forgot) the answer is **4** 😊. Let me know if you’d like a harder one next time! 🧠\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc678\"}}\n", "query = \"What math problem did I ask?\"\n", "language = \"English\"\n", "\n", "input_messages = messages + [HumanMessage(query)]\n", "output = app.invoke(\n", "    {\"messages\": input_messages, \"language\": language},\n", "    config,\n", ")\n", "output[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "2a3ee30f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}