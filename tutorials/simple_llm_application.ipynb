{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6d837c64", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"..\")\n", "\n", "import tool\n", "\n", "model = tool.get_model()"]}, {"cell_type": "code", "execution_count": null, "id": "85aba013", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='你好！  \\n嗨！  \\n\\n（根据使用场景，\"hi\" 可以翻译为较正式的“你好”或更口语化的“嗨”。如果需要其他风格的表达可以补充说明~）', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 38, 'prompt_tokens': 12, 'total_tokens': 50, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b13cf0fb3a2b15a51d776e05819e', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--cd0b9652-decc-4629-884e-d6036f55145a-0', usage_metadata={'input_tokens': 12, 'output_tokens': 38, 'total_tokens': 50, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "\n", "messages = [\n", "    SystemMessage(\"Translate the following from English to Chinese\"),\n", "    HumanMessage(\"hi!\"),\n", "]\n", "model.invoke(messages)"]}, {"cell_type": "code", "execution_count": 3, "id": "353e4290", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='I’m **DeepSeek Chat**, an AI assistant created by **DeepSeek** to help answer your questions, provide insights, and assist with various topics like research, coding, writing, and more! 😊  \\n\\nI can analyze documents (PDFs, Word, Excel, etc.), search the web when needed, and offer detailed, human-like responses. Best of all, I’m completely **free** to use!  \\n\\nHow can I help you today? 🚀', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 99, 'prompt_tokens': 7, 'total_tokens': 106, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b13ef5cf92570394ed0cabab3b30', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--7a6a5113-ceb2-48a9-90ec-504e228de62e-0', usage_metadata={'input_tokens': 7, 'output_tokens': 99, 'total_tokens': 106, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke(\"who are you?\")"]}, {"cell_type": "code", "execution_count": 5, "id": "a2805fc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='你好！  \\n嗨！  \\n你好呀！  \\n\\n（根据情境可以选择不同的翻译：\"你好\"较正式，\"嗨\"较轻松，\"你好呀\"更亲切。）', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 33, 'prompt_tokens': 12, 'total_tokens': 45, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0197b1412d45a2bc170846de8f1abf99', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--9a03ea5a-be28-4eac-982a-b290e52a02d0-0', usage_metadata={'input_tokens': 12, 'output_tokens': 33, 'total_tokens': 45, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke(\n", "    [\n", "        {\n", "            \"content\": \"Translate the following from English to Chinese\",\n", "            \"role\": \"system\",\n", "        },\n", "        {\"role\": \"user\", \"content\": \"hi!\"},\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "bc3b44d1", "metadata": {}, "source": ["### streaming"]}, {"cell_type": "code", "execution_count": 6, "id": "d82d8b9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|你好|！(Nǐ hǎo!)| 😊||"]}], "source": ["for token in model.stream(messages):\n", "    print(token.content, end=\"|\")"]}, {"cell_type": "markdown", "id": "435486ee", "metadata": {}, "source": ["### prompt template"]}, {"cell_type": "code", "execution_count": null, "id": "12365d02", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["prompt:  {'lc': 1, 'type': 'constructor', 'id': ['langchain', 'prompts', 'chat', 'ChatPromptValue'], 'kwargs': {'messages': [SystemMessage(content='Translate the following from English into Chinese', additional_kwargs={}, response_metadata={}), HumanMessage(content='hi!', additional_kwargs={}, response_metadata={})]}}\n", "prompt: Translate the following from English into Chinese,\n", " hi!\n"]}, {"data": {"text/plain": ["'嗨！'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.prompts import ChatPromptTemplate\n", "\n", "system_template = \"Translate the following from English into {language}\"\n", "\n", "prompt_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_template),\n", "    (\"user\", \"{text}\")\n", "])\n", "prompt = prompt_template.invoke({\"language\": \"Chinese\", \"text\": \"hi!\"})\n", "print(\"prompt: \", prompt.to_json())\n", "print(f\"prompt: {prompt.to_messages()[0].content},\\n {prompt.to_messages()[1].content}\")\n", "\n", "\n", "model.invoke(prompt).content\n"]}, {"cell_type": "code", "execution_count": null, "id": "2a482243", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}