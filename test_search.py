#!/usr/bin/env python3
"""
测试 DuckDuckGoSearchResults 的输出格式
"""

from langchain_community.tools import DuckDuckGoSearchResults
import json

def test_search_tool():
    """测试搜索工具的输出格式"""
    
    # 创建搜索工具
    search_tool = DuckDuckGoSearchResults(
        output_format="json",
        num_results=5
    )
    
    # 执行搜索
    query = "cloudwego/eino 仓库地址"
    print(f"搜索查询: {query}")
    print("-" * 50)
    
    try:
        # 调用搜索工具
        result = search_tool.invoke({"query": query})
        
        print("搜索结果类型:", type(result))
        print("搜索结果:")
        print(result)
        print("-" * 50)
        
        # 尝试解析为 JSON
        if isinstance(result, str):
            try:
                parsed_result = json.loads(result)
                print("解析后的 JSON 结构:")
                print(json.dumps(parsed_result, ensure_ascii=False, indent=2))
            except json.JSONDecodeError:
                print("结果不是有效的 JSON 格式")
        else:
            print("结果已经是结构化数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
    except Exception as e:
        print(f"搜索出错: {e}")

if __name__ == "__main__":
    test_search_tool()
