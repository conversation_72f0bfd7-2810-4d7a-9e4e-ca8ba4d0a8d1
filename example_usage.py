#!/usr/bin/env python3
"""
LangChain Agent 使用示例

这个文件展示了如何使用我们实现的 LangChain Agent
"""

from langchain_agent_with_tools import (
    create_todo_agent,
    demo_react_agent, 
    demo_multi_agent
)


def simple_example():
    """简单使用示例"""
    print("🚀 简单使用示例")
    print("="*50)
    
    # 创建 Agent
    agent = create_todo_agent()
    
    # 执行任务
    tasks = [
        "添加一个学习 Python 的待办事项，截止时间是下周五",
        "列出所有待办事项",
        "搜索 Python 最新版本的特性"
    ]
    
    for task in tasks:
        print(f"\n📝 任务: {task}")
        print("-" * 40)
        
        try:
            response = agent.invoke({"input": task})
            print(f"✅ 回复: {response['output'][:200]}...")
        except Exception as e:
            print(f"❌ 错误: {e}")


def interactive_example():
    """交互式示例"""
    print("\n🎮 交互式 Agent 体验")
    print("="*50)
    print("输入 'quit' 退出")
    
    agent = create_todo_agent()
    
    while True:
        try:
            user_input = input("\n💬 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
                
            if not user_input:
                continue
                
            print("🤖 Agent 正在思考...")
            response = agent.invoke({"input": user_input})
            print(f"🤖 Agent: {response['output']}")
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错了: {e}")


def multi_agent_example():
    """多 Agent 协作示例"""
    print("\n🤝 Multi Agent 协作示例")
    print("="*50)
    
    coordinator = demo_multi_agent()
    
    test_cases = [
        "添加一个学习机器学习的待办事项",  # 路由到待办事项 Agent
        "搜索最新的 AI 技术趋势",         # 路由到搜索 Agent
        "列出所有未完成的任务",           # 路由到待办事项 Agent
    ]
    
    for case in test_cases:
        print(f"\n📝 输入: {case}")
        print("-" * 40)
        
        try:
            result = coordinator(case)
            print(f"✅ 结果: {result[:200]}...")
        except Exception as e:
            print(f"❌ 错误: {e}")


def performance_comparison():
    """性能对比示例"""
    import time
    
    print("\n⚡ 性能对比示例")
    print("="*50)
    
    # 创建不同类型的 Agent
    standard_agent = create_todo_agent()
    multi_agent = demo_multi_agent()
    
    test_input = "添加一个学习数据科学的待办事项"
    
    # 测试标准 Agent
    print("🔧 测试标准 Tool Calling Agent...")
    start_time = time.time()
    try:
        result1 = standard_agent.invoke({"input": test_input})
        time1 = time.time() - start_time
        print(f"⏱️ 耗时: {time1:.2f}秒")
        print(f"✅ 结果长度: {len(result1['output'])} 字符")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n" + "-"*30)
    
    # 测试 Multi Agent
    print("🤝 测试 Multi Agent 协作...")
    start_time = time.time()
    try:
        result2 = multi_agent(test_input)
        time2 = time.time() - start_time
        print(f"⏱️ 耗时: {time2:.2f}秒")
        print(f"✅ 结果长度: {len(result2)} 字符")
    except Exception as e:
        print(f"❌ 错误: {e}")


def main():
    """主函数"""
    print("🎯 LangChain Agent 使用示例")
    print("="*60)
    
    examples = {
        "1": ("简单使用示例", simple_example),
        "2": ("交互式体验", interactive_example),
        "3": ("Multi Agent 协作", multi_agent_example),
        "4": ("性能对比", performance_comparison),
    }
    
    print("\n请选择要运行的示例:")
    for key, (name, _) in examples.items():
        print(f"{key}. {name}")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice in examples:
        name, func = examples[choice]
        print(f"\n🚀 运行 {name}...")
        func()
    else:
        print("❌ 无效选择，运行默认示例...")
        simple_example()


if __name__ == "__main__":
    main()
