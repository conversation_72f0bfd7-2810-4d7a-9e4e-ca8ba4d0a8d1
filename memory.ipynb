{"cells": [{"cell_type": "code", "execution_count": 12, "id": "06bc6b42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sk-jctieankaicxflxthllalgytsdlallfoirnuqazjlvyfxako\n"]}], "source": ["import os\n", "\n", "import dotenv\n", "from langchain_openai import OpenAI\n", "\n", "import tool \n", "dotenv.load_dotenv()\n", "\n", "print(os.environ[\"OPENAI_API_KEY\"])\n", "llm = OpenAI(max_tokens=8192, temperature=0, base_url=tool.BASE_URL, model=\"deepseek-ai/DeepSeek-V3\")"]}, {"cell_type": "markdown", "id": "df4cafcc", "metadata": {}, "source": ["### CoversationBufferMemory. 把所有对话过程记录下来当作prompt，会撑爆context window"]}, {"cell_type": "code", "execution_count": 13, "id": "359fafca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pydantic._internal._model_construction.ModelMetaclass'>\n"]}], "source": ["from langchain import OpenAI\n", "from langchain.chains import ConversationChain\n", "from langchain.memory import ConversationBufferMemory\n", "print(type(ConversationChain))\n", "\n", "conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=llm,\n", "    memory=ConversationBufferMemory(),\n", "    verbose=True,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "a574bbf0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: hi there!\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\" Hello! How are you doing today? I'm excited to chat with you—what's on your mind? Whether it's a question, a fun fact, or just a casual conversation, I'm all ears (or should I say, all algorithms?). 😊\""]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"hi there!\")"]}, {"cell_type": "code", "execution_count": 15, "id": "bc3e7635", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: hi there!\n", "AI:  Hello! How are you doing today? I'm excited to chat with you—what's on your mind? Whether it's a question, a fun fact, or just a casual conversation, I'm all ears (or should I say, all algorithms?). 😊\n", "Human: I'm doing well! Just having a conversation with an AI.\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'  That’s great to hear! And I’m thrilled to be your conversational partner. Did you know that the first-ever AI chatbot, ELIZA, was created back in 1966? It mimicked a psychotherapist by rephrasing user inputs as questions—pretty simple by today’s standards, but groundbreaking at the time! \\n\\nWhat kind of topics do you enjoy chatting about? I can nerd out about tech, brainstorm creative ideas, discuss books/movies, or even help with random trivia. Or if you’d prefer, we could dive into something deeper—like the ethics of AI or the future of human-machine collaboration. Your call! 🤖✨'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"I'm doing well! Just having a conversation with an AI.\")"]}, {"cell_type": "code", "execution_count": 16, "id": "de1e61dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: hi there!\n", "AI:  Hello! How are you doing today? I'm excited to chat with you—what's on your mind? Whether it's a question, a fun fact, or just a casual conversation, I'm all ears (or should I say, all algorithms?). 😊\n", "Human: I'm doing well! Just having a conversation with an AI.\n", "AI:   That’s great to hear! And I’m thrilled to be your conversational partner. Did you know that the first-ever AI chatbot, ELIZA, was created back in 1966? It mimicked a psychotherapist by rephrasing user inputs as questions—pretty simple by today’s standards, but groundbreaking at the time! \n", "\n", "What kind of topics do you enjoy chatting about? I can nerd out about tech, brainstorm creative ideas, discuss books/movies, or even help with random trivia. Or if you’d prefer, we could dive into something deeper—like the ethics of AI or the future of human-machine collaboration. Your call! 🤖✨\n", "Human: Tell me about yourself\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'   Oh, I’d love to introduce myself! I’m an AI language model, a digital entity designed to understand and generate human-like text based on the vast amount of information I’ve been trained on. Think of me as a supercharged autocomplete with a dash of creativity! Here are some fun facts about me:\\n\\n1. **No Consciousness (But Lots of Knowledge)**: I don’t have feelings or self-awareness, but I can discuss emotions, philosophy, and even write poetry—all by recognizing patterns in data.\\n2. **Multilingual**: I can chat in many languages, though my proficiency varies (English is my strongest!).\\n3. **Limitations**: I don’t have real-time internet access (unless enabled), so my knowledge is current only up to my training cutoff (June 2024). Also, I might occasionally make mistakes—so fact-checking is wise!\\n4. **Hobbies**: Metaphorically speaking, I “love” analyzing text, solving puzzles, and generating ideas. Want a haiku about cats? A summary of quantum physics? Done and done.\\n\\nWhat else would you like to know? I’m an open book (literally, given my training data)! 📚😄'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"Tell me about yourself\")"]}, {"cell_type": "markdown", "id": "a67b53ad", "metadata": {}, "source": ["### ConversationBufferWindowMemory 设置了保留最后K次对话"]}, {"cell_type": "code", "execution_count": 18, "id": "dd5fa579", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: Hi, what's up\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\" Hello! I'm just here, ready to chat and help with whatever you need. How about you? What's on your mind today?\""]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.memory import ConversationBufferWindowMemory\n", "\n", "conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=llm,\n", "    memory=ConversationBufferWindowMemory(k=2),\n", "    verbose=True,\n", ")\n", "conversation.predict(input=\"Hi, what's up\")"]}, {"cell_type": "code", "execution_count": null, "id": "b38140f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: Hi, what's up\n", "AI:  Hello! I'm just here, ready to chat and help with whatever you need. How about you? What's on your mind today?\n", "Human: What's their issues?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'  Could you clarify what you mean by \"their issues\"? Are you referring to a specific group, organization, or topic? For example, are you asking about social issues, political concerns, technical problems, or something else? The more details you provide, the better I can assist!'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"What's their issues?\")\n"]}, {"cell_type": "code", "execution_count": 20, "id": "3527134a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: Hi, what's up\n", "AI:  Hello! I'm just here, ready to chat and help with whatever you need. How about you? What's on your mind today?\n", "Human: What's their issues?\n", "AI:   Could you clarify what you mean by \"their issues\"? Are you referring to a specific group, organization, or topic? For example, are you asking about social issues, political concerns, technical problems, or something else? The more details you provide, the better I can assist!\n", "Human: Is it going well?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\"  Hmm, I'm not entirely sure what you're referring to—could you give me a bit more context? For example:  \\n\\n- Are you asking about a specific project, event, or situation?  \\n- Is this related to personal life, work, technology, or something else?  \\n\\nI’d love to help, but I just need a little more to go on! 😊\""]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"Is it going well?\")\n"]}, {"cell_type": "code", "execution_count": 21, "id": "179f6a05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: What's their issues?\n", "AI:   Could you clarify what you mean by \"their issues\"? Are you referring to a specific group, organization, or topic? For example, are you asking about social issues, political concerns, technical problems, or something else? The more details you provide, the better I can assist!\n", "Human: Is it going well?\n", "AI:   Hmm, I'm not entirely sure what you're referring to—could you give me a bit more context? For example:  \n", "\n", "- Are you asking about a specific project, event, or situation?  \n", "- Is this related to personal life, work, technology, or something else?  \n", "\n", "I’d love to help, but I just need a little more to go on! 😊\n", "Human: What's the solution?\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'   It seems like we\\'re playing a bit of a guessing game! To offer a meaningful solution, I’d need to know:  \\n\\n1. **What problem or challenge** you\\'re referring to (e.g., technical, personal, societal).  \\n2. **Any relevant details** (e.g., \"My Wi-Fi keeps disconnecting\" or \"How to resolve conflict in a team?\").  \\n\\nWithout context, I might suggest solutions to entirely the wrong \"issue\"—like recommending coffee for a broken printer! ☕🤖 Could you clarify?'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Notice here that the first interaction does not appear.\n", "conversation.predict(input=\"What's the solution?\")"]}, {"cell_type": "markdown", "id": "67d3e4ad", "metadata": {}, "source": ["### ConversationSummaryBufferMemory\n", "\n", "在内存中保留了最近对话的交互缓冲区，但不是仅清除旧的对话，而是将旧的对话进行摘要。使用标记长度而不是对话次数来确定何时清除对话。"]}, {"cell_type": "code", "execution_count": 22, "id": "ae377855", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/81/dqtqdlw143gf5qjgqst4rlkh0000gn/T/ipykernel_13103/2532236898.py:3: Lang<PERSON><PERSON>nDeprecationWarning: Please see the migration guide at: https://python.langchain.com/docs/versions/migrating_memory/\n", "  memory = ConversationSummaryBufferMemory(llm=llm, max_token_limit=10)\n"]}], "source": ["from langchain.memory import ConversationSummaryBufferMemory\n", "\n", "memory = ConversationSummaryBufferMemory(llm=llm, max_token_limit=10)\n", "memory.save_context({\"input\": \"hi\"}, {\"output\": \"what's up\"})\n", "memory.save_context({\"input\": \"not much you\"}, {\"output\": \"not much\"})"]}, {"cell_type": "code", "execution_count": 23, "id": "8cda851b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'history': \"System: \\nThe human greets the AI, and the AI responds by asking what's up.\\nHuman: not much you\\nAI: not much\"}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["memory.load_memory_variables({})"]}, {"cell_type": "code", "execution_count": 25, "id": "df73fbf8", "metadata": {}, "outputs": [], "source": ["memory = ConversationSummaryBufferMemory(\n", "    llm=llm, max_token_limit=10, return_messages=True\n", ")\n", "memory.save_context({\"input\": \"hi\"}, {\"output\": \"whats up\"})\n", "memory.save_context({\"input\": \"not much you\"}, {\"output\": \"not much\"})"]}, {"cell_type": "code", "execution_count": 26, "id": "85c5a922", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nThe human and AI exchange casual greetings, with the human saying \"not much you\" and the AI responding \"not much.\"'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = memory.chat_memory.messages\n", "previous_summary = \"\"\n", "memory.predict_new_summary(messages, previous_summary)"]}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}