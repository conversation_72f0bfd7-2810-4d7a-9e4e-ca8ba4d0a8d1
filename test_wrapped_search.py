#!/usr/bin/env python3
"""
测试包装后的搜索工具
"""

import sys
sys.path.append('.')

from agent import search_tool
import json

def test_wrapped_search():
    """测试包装后的搜索工具"""
    
    query = "cloudwego/eino 仓库地址"
    print(f"搜索查询: {query}")
    print("-" * 50)
    
    try:
        # 调用包装后的搜索工具
        result = search_tool.invoke({"query": query})
        
        print("搜索结果类型:", type(result))
        print("搜索结果:")
        print(result)
        print("-" * 50)
        
        # 验证 JSON 格式
        try:
            parsed_result = json.loads(result)
            print("✅ 结果是有效的 JSON 格式")
            print("消息:", parsed_result.get("message"))
            print("结果数量:", len(parsed_result.get("results", [])))
            
            # 显示前几个结果
            for i, item in enumerate(parsed_result.get("results", [])[:3]):
                print(f"\n结果 {i+1}:")
                print(f"  标题: {item.get('title')}")
                print(f"  URL: {item.get('url')}")
                print(f"  摘要: {item.get('summary')[:100]}...")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析错误: {e}")
            
    except Exception as e:
        print(f"❌ 搜索出错: {e}")

if __name__ == "__main__":
    test_wrapped_search()
