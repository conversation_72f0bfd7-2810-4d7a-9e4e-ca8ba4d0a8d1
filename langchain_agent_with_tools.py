#!/usr/bin/env python3
"""
LangChain 实现的 Agent - 让大模型拥有双手

基于 CloudWeGo Eino 文档的 LangChain 等价实现
文档地址: https://www.cloudwego.io/zh/docs/eino/quick_start/agent_llm_with_tools/

这个示例展示了如何使用 LangChain 构建一个具有工具调用能力的 Agent，
包含了多种工具实现方式和完整的 Agent 构建流程。
"""

import json
import os
from typing import Optional, List, Dict, Any
from datetime import datetime

from pydantic import BaseModel, Field
from langchain_core.tools import tool as langchain_tool, StructuredTool, BaseTool
from langchain_core.tools.base import ArgsSchema
from langchain_core.callbacks import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.tools import DuckDuckGoSearchResults

import tool  # 导入我们的模型配置


# ============================================================================
# Tool 实现方式一：使用 @langchain_tool 装饰器（类似 Eino 的 NewTool）
# ============================================================================

class TodoAddParams(BaseModel):
    """添加待办事项的参数"""
    content: str = Field(description="待办事项的内容")
    started_at: Optional[int] = Field(
        None, description="开始时间，Unix 时间戳"
    )
    deadline: Optional[int] = Field(
        None, description="截止时间，Unix 时间戳"
    )


@langchain_tool
def add_todo_tool(params: TodoAddParams) -> str:
    """添加一个待办事项"""
    # Mock 处理逻辑
    result = {
        "msg": "add todo success",
        "todo": {
            "id": "todo_" + str(datetime.now().timestamp()),
            "content": params.content,
            "started_at": params.started_at,
            "deadline": params.deadline,
            "done": False
        }
    }
    return json.dumps(result, ensure_ascii=False)


# ============================================================================
# Tool 实现方式二：使用 StructuredTool.from_function（类似 Eino 的 InferTool）
# ============================================================================

class TodoUpdateParams(BaseModel):
    """更新待办事项的参数"""
    id: str = Field(description="待办事项的 ID")
    content: Optional[str] = Field(default=None, description="待办事项内容")
    started_at: Optional[int] = Field(
        default=None, description="开始时间，Unix 时间戳"
    )
    deadline: Optional[int] = Field(
        default=None, description="截止时间，Unix 时间戳"
    )
    done: Optional[bool] = Field(default=None, description="完成状态")


def update_todo_func(params: TodoUpdateParams) -> str:
    """更新待办事项，例如：内容、截止时间等"""
    # Mock 处理逻辑
    result = {
        "msg": "update todo success",
        "updated_fields": {
            "id": params.id,
            "content": params.content,
            "started_at": params.started_at,
            "deadline": params.deadline,
            "done": params.done
        }
    }
    return json.dumps(result, ensure_ascii=False)


# 使用 StructuredTool 创建工具
update_todo_tool = StructuredTool.from_function(
    name="update_todo",
    description="更新待办事项，例如：内容、截止时间等",
    func=update_todo_func
)


# ============================================================================
# Tool 实现方式三：实现 BaseTool 接口（类似 Eino 的 Tool 接口实现）
# ============================================================================

class TodoListInput(BaseModel):
    """列出待办事项的参数"""
    finished: Optional[bool] = Field(
        default=None, description="过滤已完成的待办事项"
    )


class ListTodoTool(BaseTool):
    """列出所有待办事项的工具"""
    name: str = "list_todo"
    description: str = "列出所有待办事项"
    args_schema: Optional[ArgsSchema] = TodoListInput
    
    def _run(
        self, 
        finished: Optional[bool] = None, 
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """执行工具逻辑"""
        # Mock 数据
        todos = [
            {
                "id": "1",
                "content": "在2024年12月10日之前完成Eino项目演示文稿的准备工作",
                "started_at": 1717401600,
                "deadline": 1717488000,
                "done": False
            },
            {
                "id": "2", 
                "content": "学习 LangChain Agent 开发",
                "started_at": 1717401600,
                "deadline": 1717574400,
                "done": True
            }
        ]
        
        # 根据 finished 参数过滤
        if finished is not None:
            todos = [todo for todo in todos if todo["done"] == finished]
        
        result = {"todos": todos}
        return json.dumps(result, ensure_ascii=False)
    
    async def _arun(
        self, 
        finished: Optional[bool] = None,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """异步执行"""
        return self._run(finished, run_manager=None)


# ============================================================================
# Tool 实现方式四：使用官方封装的工具（类似 Eino 的官方工具）
# ============================================================================

def create_search_tool() -> BaseTool:
    """创建搜索工具，类似 Eino 的 duckduckgo 工具"""
    base_search_tool = DuckDuckGoSearchResults(
        output_format="json",
        num_results=5
    )
    
    @langchain_tool
    def search_tool(query: str) -> str:
        """搜索信息并返回结构化的 JSON 格式结果"""
        try:
            # 调用基础搜索工具
            raw_result = base_search_tool.invoke({"query": query})
            
            # 解析原始结果
            if isinstance(raw_result, str):
                results_list = json.loads(raw_result)
            else:
                results_list = raw_result
            
            # 转换为目标格式
            formatted_result = {
                "message": f"Found {len(results_list)} results successfully.",
                "results": []
            }
            
            for item in results_list:
                formatted_item = {
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "summary": item.get("snippet", "")
                }
                formatted_result["results"].append(formatted_item)
            
            return json.dumps(formatted_result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            error_result = {
                "message": f"搜索出错: {str(e)}",
                "results": []
            }
            return json.dumps(error_result, ensure_ascii=False, indent=2)
    
    return search_tool


# ============================================================================
# Agent 构建函数
# ============================================================================

def create_todo_agent():
    """创建待办事项管理 Agent"""
    
    # 1. 初始化所有工具
    tools = [
        add_todo_tool,          # 方式一：@langchain_tool 装饰器
        update_todo_tool,       # 方式二：StructuredTool.from_function
        ListTodoTool(),         # 方式三：实现 BaseTool 接口
        create_search_tool(),   # 方式四：官方封装的工具
    ]
    
    # 2. 创建并配置 ChatModel
    llm = tool.get_model()
    
    # 3. 创建 Agent 的 Prompt 模板
    prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一个智能的待办事项管理助手。你可以帮助用户：
        1. 添加新的待办事项
        2. 更新现有的待办事项
        3. 列出待办事项
        4. 搜索相关信息
        
        请根据用户的需求选择合适的工具来完成任务。
        在使用工具时，请确保参数正确，并将结果以友好的方式呈现给用户。"""),
        ("user", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ])
    
    # 4. 创建 Agent
    agent = create_tool_calling_agent(llm=llm, tools=tools, prompt=prompt)
    
    # 5. 创建 AgentExecutor
    agent_executor = AgentExecutor(
        agent=agent, 
        tools=tools, 
        verbose=True,
        handle_parsing_errors=True
    )
    
    return agent_executor


# ============================================================================
# 主函数和示例
# ============================================================================

def main():
    """主函数 - 运行 Agent 示例"""
    
    print("🤖 创建待办事项管理 Agent...")
    agent = create_todo_agent()
    
    # 测试用例
    test_cases = [
        "添加一个学习 Eino 的 TODO，同时搜索一下 cloudwego/eino 的仓库地址",
        "列出所有未完成的待办事项",
        "更新 ID 为 1 的待办事项，将其标记为已完成",
        "搜索 LangChain Agent 的最新教程"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"🧪 测试用例 {i}: {test_input}")
        print('='*60)
        
        try:
            response = agent.invoke({"input": test_input})
            print(f"\n✅ Agent 回复:")
            print(response.get("output", "无回复"))
            
        except Exception as e:
            print(f"\n❌ 执行出错: {e}")
        
        print("\n" + "-"*60)


def demo_react_agent():
    """演示 ReAct Agent 的实现（类似 Eino 的 ReAct Agent）"""
    from langchain.agents import create_react_agent
    from langchain_core.prompts import PromptTemplate

    # 创建工具
    tools = [
        add_todo_tool,
        ListTodoTool(),
        create_search_tool(),
    ]

    # 创建 ReAct 风格的 prompt
    react_prompt = PromptTemplate.from_template("""
你是一个智能助手，可以使用以下工具来帮助用户：

{tools}

使用以下格式：

Question: 用户的问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是 [{tool_names}] 中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个 Thought/Action/Action Input/Observation 可以重复 N 次)
Thought: 我现在知道最终答案了
Final Answer: 对原始问题的最终答案

开始！

Question: {input}
Thought: {agent_scratchpad}
""")

    llm = tool.get_model()

    # 创建 ReAct Agent
    react_agent = create_react_agent(llm, tools, react_prompt)
    react_executor = AgentExecutor(
        agent=react_agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True
    )

    return react_executor


def demo_multi_agent():
    """演示多 Agent 协作（类似 Eino 的 Multi Agent）"""

    # 专门处理待办事项的 Agent
    todo_agent = create_todo_agent()

    # 专门处理搜索的 Agent
    search_tools = [create_search_tool()]
    search_prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个专业的搜索助手，专门帮助用户搜索和获取信息。"),
        ("user", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ])

    llm = tool.get_model()
    search_agent = create_tool_calling_agent(llm=llm, tools=search_tools, prompt=search_prompt)
    search_executor = AgentExecutor(agent=search_agent, tools=search_tools, verbose=True)

    # 协调器 Agent
    def coordinator_agent(user_input: str) -> str:
        """协调多个 Agent 的工作"""

        # 简单的路由逻辑
        if any(keyword in user_input.lower() for keyword in ["todo", "待办", "任务"]):
            print("🎯 路由到待办事项 Agent")
            response = todo_agent.invoke({"input": user_input})
            return response.get("output", "")

        elif any(keyword in user_input.lower() for keyword in ["搜索", "search", "查找"]):
            print("🎯 路由到搜索 Agent")
            response = search_executor.invoke({"input": user_input})
            return response.get("output", "")

        else:
            # 默认使用综合 Agent
            print("🎯 路由到综合 Agent")
            response = todo_agent.invoke({"input": user_input})
            return response.get("output", "")

    return coordinator_agent


def run_comparison_demo():
    """运行对比演示，展示不同 Agent 实现方式"""

    print("🚀 LangChain Agent 实现对比演示")
    print("="*80)

    # 1. 标准 Tool Calling Agent
    print("\n1️⃣ 标准 Tool Calling Agent（类似 Eino Chain 构建）")
    standard_agent = create_todo_agent()

    # 2. ReAct Agent
    print("\n2️⃣ ReAct Agent（类似 Eino ReAct Agent）")
    react_agent = demo_react_agent()

    # 3. Multi Agent
    print("\n3️⃣ Multi Agent 协作（类似 Eino Multi Agent）")
    multi_agent = demo_multi_agent()

    # 测试输入
    test_input = "添加一个学习 LangChain 的待办事项"

    print(f"\n🧪 测试输入: {test_input}")
    print("="*80)

    # 测试标准 Agent
    print("\n📋 标准 Tool Calling Agent 结果:")
    try:
        result1 = standard_agent.invoke({"input": test_input})
        print(f"✅ 结果: {result1.get('output', '')[:200]}...")
    except Exception as e:
        print(f"❌ 错误: {e}")

    print("\n" + "-"*60)

    # 测试 Multi Agent
    print("\n🤝 Multi Agent 协作结果:")
    try:
        result3 = multi_agent(test_input)
        print(f"✅ 结果: {result3[:200]}...")
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    # 可以选择运行不同的演示
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "comparison":
        run_comparison_demo()
    else:
        main()
