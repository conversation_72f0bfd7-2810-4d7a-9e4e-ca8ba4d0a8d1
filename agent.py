from typing import Optional
from pydantic import BaseModel, Field
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.agents.output_parsers.base import AgentOutputParser
from langchain_openai import ChatOpenAI
import json

# 自定义 OutputParser，尝试把文本解析成 JSON
class JsonOutputParser(AgentOutputParser):
    def parse(self, text: str):
        try:
            return json.loads(text)
        except Exception:
            return text

    def get_format_instructions(self) -> str:
        return "请仅返回符合 JSON 格式的结果。"

# 定义工具参数模型
class SearchParams(BaseModel):
    query: str = Field(description="搜索关键词")

# 定义工具，返回 JSON 格式字符串
@tool
def search_tool(params: SearchParams) -> str:
    # 这里模拟返回结构化的搜索结果JSON字符串
    return json.dumps({
        "message": "Found 10 results successfully.",
        "results": [
            {"title": "GitHub - cloudwego/eino", "url": "https://github.com/cloudwego/eino", "summary": "Eino 是一个 Golang LLM 框架。"},
            {"title": "Eino 官方文档", "url": "https://www.cloudwego.io/zh/docs/eino/overview/", "summary": "Eino 终极 LLM 应用开发框架。"},
        ]
    }, ensure_ascii=False, indent=2)

def main():
    # 初始化模型
    llm = ChatOpenAI(model="gpt-4o")

    tools = [search_tool]

    # 定义 prompt，必须包含 {input} 和 {agent_scratchpad}
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一个可以调用工具的智能助理。"),
        ("user", "{input}"),
        ("assistant", "{agent_scratchpad}"),
    ])

    # 创建 Agent，传入自定义 output_parser
    agent = create_tool_calling_agent(
        llm=llm,
        tools=tools,
        prompt=prompt,
        output_parser=JsonOutputParser(),
    )

    executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

    # 调用 Agent
    response = executor.invoke({"input": "请搜索 cloudwego/eino 的仓库地址"})
    print("最终返回结果（解析后）：")
    print(response["output"])  # 这是 dict 或 JSON 字符串

if __name__ == "__main__":
    main()
