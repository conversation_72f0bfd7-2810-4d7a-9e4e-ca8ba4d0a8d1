from typing import Optional, Sequence
from pydantic import BaseModel, Field
from langchain_core.tools import tool, StructuredTool, BaseTool
from langchain_core.prompts import ChatPromptTemplate
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.agents.output_parsers.base import AgentOutputParser
from langchain.agents.format_scratchpad.openai_tools import format_to_tool_messages
from langchain_openai import ChatOpenAI
import json


# -------- 定义工具参数和工具 --------

class TodoAddParams(BaseModel):
    content: str = Field(description="待办内容")
    start_at: Optional[int] = Field(None, description="开始时间，unix时间戳")
    deadline: Optional[int] = Field(None, description="截止时间，unix时间戳")

@tool
def add_todo(params: TodoAddParams) -> str:
    # 这里模拟新增待办
    return "添加待办成功"

class TodoUpdateParams(BaseModel):
    id: str = Field(description="待办ID")
    content: Optional[str] = Field(None, description="内容")
    start_at: Optional[int] = Field(None, description="开始时间")
    deadline: Optional[int] = Field(None, description="截止时间")
    done: Optional[bool] = Field(None, description="是否完成")

def update_todo_func(params: TodoUpdateParams) -> str:
    return "更新待办成功"

update_todo_tool = StructuredTool.from_function(name="update_todo", func=update_todo_func)

class TodoListInput(BaseModel):
    finished: Optional[bool] = Field(None, description="是否已完成")

class TodoListTool(BaseTool):
    name: str = "list_todo"
    description: str = "列出所有待办事项"
    args_schema = TodoListInput
    return_direct = True

    def _run(self, finished: Optional[bool] = None) -> str:
        # 模拟返回结构化待办列表 JSON
        return json.dumps({
            "todos": [
                {
                    "id": "1",
                    "content": "在2024年12月10日前完成Eino项目演示文稿",
                    "started_at": 1717401600,
                    "deadline": 1717488000,
                    "done": False
                }
            ]
        }, ensure_ascii=False)

    async def _arun(self, finished: Optional[bool] = None) -> str:
        return self._run(finished)


todo_list_tool = TodoListTool()


# DuckDuckGo搜索工具示例（使用 langchain_community）

from langchain_community.tools import DuckDuckGoSearchResults

base_search_tool = DuckDuckGoSearchResults(output_format="json", num_results=10)

@tool
def search_tool(query: str) -> str:
    raw = base_search_tool.invoke({"query": query})
    # 解析原始结果，格式化成符合需求的 JSON 字符串
    if isinstance(raw, str):
        results_list = json.loads(raw)
    else:
        results_list = raw
    formatted = {
        "message": f"Found {len(results_list)} results successfully.",
        "results": []
    }
    for item in results_list:
        formatted["results"].append({
            "title": item.get("title", ""),
            "url": item.get("link", ""),
            "summary": item.get("snippet", ""),
        })
    return json.dumps(formatted, ensure_ascii=False, indent=2)


# -------- 自定义 OutputParser 解析 JSON --------

class JsonOutputParser(AgentOutputParser):
    def parse(self, text: str):
        try:
            return json.loads(text)
        except Exception:
            return text

    def get_format_instructions(self) -> str:
        return "请仅返回符合 JSON 格式的结果。"


# -------- 构造 Prompt --------

prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一个可以调用工具的智能助理。"),
    ("user", "{input}"),
    ("assistant", "{agent_scratchpad}"),
])

# -------- 创建 Agent --------

tools = [add_todo, update_todo_tool, todo_list_tool, search_tool]
llm = ChatOpenAI(model="gpt-4o")

agent = create_tool_calling_agent(
    llm=llm,
    tools=tools,
    prompt=prompt,
    message_formatter=format_to_tool_messages,
    output_parser=JsonOutputParser(),
)

executor = AgentExecutor(agent=agent, tools=tools, verbose=True)


# -------- 测试调用 --------

if __name__ == "__main__":
    query = "添加一个学习 Eino 的待办，同时搜索 cloudwego/eino 仓库地址"
    response = executor.invoke({"input": query})
    print("最终返回：")
    print(response["output"])
