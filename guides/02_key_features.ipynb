{"cells": [{"cell_type": "code", "execution_count": 3, "id": "459da189", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"..\")\n", "\n", "import tool\n", "\n", "llm = tool.get_model()"]}, {"cell_type": "markdown", "id": "b157ea52", "metadata": {}, "source": ["## Pydantic class"]}, {"cell_type": "code", "execution_count": 8, "id": "604e0c31", "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why don’t cats ever get billed by their therapists?', punchline='Because they always land on their feet... of the bill!', rating=4)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Optional\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "    \n", "    setup: str = Field(description=\"The setup of the joke\")\n", "    punchline: str = Field(description=\"The punchline to the joke\")\n", "    rating: Optional[int]= Field(\n", "        default=None, description=\"How funny the joke is, from 1 to 10\"\n", "    )\n", "\n", "structured_llm = llm.with_structured_output(Joke)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")\n"]}, {"cell_type": "markdown", "id": "84243dc3", "metadata": {}, "source": ["## TypedD<PERSON> or JSON Schema"]}, {"cell_type": "code", "execution_count": 10, "id": "f4820300", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'punchline': 'Because they are just *purr-fect* on their own!',\n", " 'setup': \"Why don't cats need programmers?\"}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Optional\n", "\n", "from typing_extensions import Annotated, TypedDict\n", "\n", "\n", "class Joke(TypedDict):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: Annotated[str, ..., \"The setup of the joke\"]\n", "\n", "    # Alternatively, we could have specified setup as:\n", "    # setup: str # no default, no description\n", "    # setup: Annotated[str, ...] # no default, no description\n", "    # setup: Annotated[str, \"foo\"] # default, no description\n", "    \n", "    punchline: Annotated[str, ..., \"The punchline of the joke\"]\n", "    rating: Annotated[Optional[int], None, \"How funny the joke is, from 1 to 10\"]\n", "\n", "structured_llm = llm.with_structured_output(Joke)\n", "structured_llm.invoke(\"Tell a joke about cats\")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "7a72ad86", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'punchline': \"It's purr-fectly flat!\",\n", " 'setup': 'Why did the cat bring a level to the vet?'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# with json schema.\n", "json_schema = {\n", "    \"title\": \"joke\",\n", "    \"description\": \"Joke to tell user.\",\n", "    \"type\": \"object\",\n", "    \"properties\": {\n", "        \"setup\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The setup of the joke\",\n", "        },\n", "        \"punchline\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The punchline to the joke\",\n", "        },\n", "        \"rating\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"How funny the joke is, from 1 to 10\",\n", "            \"default\": None,\n", "        },\n", "    },\n", "    \"required\": [\"setup\", \"punchline\"],\n", "}\n", "structured_llm = llm.with_structured_output(json_schema)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")"]}, {"cell_type": "markdown", "id": "3280e6f5", "metadata": {}, "source": ["## Choosing between multiple schemas"]}, {"cell_type": "code", "execution_count": 15, "id": "f8dc4032", "metadata": {}, "outputs": [{"data": {"text/plain": ["FieldRepsonse(final_output=ConversationalResponse(response=\"Why don't cats play poker in the jungle? Too many cheetahs!\"))"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Union, Optional\n", "from pydantic import BaseModel\n", "\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "    setup: str = Field(description=\"The setup of the joke\")\n", "    punchline: str = Field(description=\"The punchline to the joke\")\n", "    rating: Optional[int] = Field(\n", "        default=None,\n", "        description=\"How funny the joke is, from 1 to 10\"\n", "    )\n", "\n", "\n", "class ConversationalResponse(BaseModel):\n", "    \"\"\"<PERSON><PERSON><PERSON> in a conversational manner. Be kind and helpful.\"\"\"\n", "    response: str = Field(description=\"A conversational response to the user's query\")\n", "\n", "class FieldRepsonse(BaseModel):\n", "    final_output: Union[Joke, ConversationalResponse]\n", "\n", "structured_llm = llm.with_structured_output(FieldRepsonse)\n", "structured_llm.invoke(\"Tell me a joke about cats\")\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "id": "befcb5c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'final_output': {'response': 'Why don’t cats play poker in the jungle? Too many cheetahs! 😸'}}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# using TypedDict\n", "from typing import Optional, Union\n", "\n", "from typing_extensions import Annotated, TypedDict\n", "\n", "\n", "class Joke(TypedDict):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: Annotated[str, ..., \"The setup of the joke\"]\n", "    punchline: Annotated[str, ..., \"The punchline of the joke\"]\n", "    rating: Annotated[Optional[int], None, \"How funny the joke is, from 1 to 10\"]\n", "\n", "\n", "class ConversationalResponse(TypedDict):\n", "    \"\"\"<PERSON><PERSON><PERSON> in a conversational manner. Be kind and helpful.\"\"\"\n", "\n", "    response: Annotated[str, ..., \"A conversational response to the user's query\"]\n", "\n", "\n", "class FinalResponse(TypedDict):\n", "    final_output: Union[Joke, ConversationalResponse]\n", "\n", "\n", "structured_llm = llm.with_structured_output(FinalResponse)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")"]}, {"cell_type": "markdown", "id": "6ca2e0d4", "metadata": {}, "source": ["## Streaming"]}, {"cell_type": "code", "execution_count": 18, "id": "299e7324", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{}\n", "{'punchline': ''}\n", "{'punchline': 'Because'}\n", "{'punchline': 'Because they'}\n", "{'punchline': \"Because they're\"}\n", "{'punchline': \"Because they're afraid\"}\n", "{'punchline': \"Because they're afraid of\"}\n", "{'punchline': \"Because they're afraid of '\"}\n", "{'punchline': \"Because they're afraid of 'dog\"}\n", "{'punchline': \"Because they're afraid of 'dog-\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital'\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': ''}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': 'Why'}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': 'Why don'}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't cats\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't cats write\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't cats write with\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't cats write with pencils\"}\n", "{'punchline': \"Because they're afraid of 'dog-ital' consequences!\", 'setup': \"Why don't cats write with pencils?\"}\n"]}], "source": ["from typing_extensions import Annotated, TypedDict\n", "\n", "class Joke(TypedDict):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: Annotated[str, ..., \"The setup of the joke\"]\n", "    punchline: Annotated[str, ..., \"The punchline of the joke\"]\n", "    rating: Annotated[Optional[int], None, \"How funny the joke is, from 1 to 10\"]\n", "\n", "structured_llm = llm.with_structured_output(Joke)\n", "\n", "for chunk in structured_llm.stream(\"Tell me a joke about cats\"):\n", "    print(chunk)\n", "    "]}, {"cell_type": "markdown", "id": "d3159675", "metadata": {}, "source": ["## Few-shot prompting"]}, {"cell_type": "code", "execution_count": 23, "id": "3e5d91a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'punchline': '<PERSON><PERSON><PERSON> who’s there? Stop knocking, I’m not a tree-mendous fan of your jokes!',\n", " 'rating': 8,\n", " 'setup': '<PERSON><PERSON><PERSON>'}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "system = \"\"\"You are a hilarious comedian. Your specialty is knock-knock jokes. \\\n", "Return a joke which has the setup (the response to \"Who's there?\") and the final punchline (the response to \"<setup> who?\").\n", "\n", "Here are some examples of jokes:\n", "\n", "example_user: Tell me a joke about planes\n", "example_assistant: {{\"setup\": \"Why don't planes ever get tired?\", \"punchline\": \"Because they have rest wings!\", \"rating\": 2}}\n", "\n", "example_user: Tell me another joke about planes\n", "example_assistant: {{\"setup\": \"Cargo\", \"punchline\": \"Cargo 'vroom vroom', but planes go 'zoom zoom'!\", \"rating\": 10}}\n", "\n", "example_user: Now about caterpillars\n", "example_assistant: {{\"setup\": \"Cat<PERSON>pillar\", \"punchline\": \"<PERSON><PERSON><PERSON><PERSON> really slow, but watch me turn into a butterfly and steal the show!\", \"rating\": 5}}\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", \"{input}\")])\n", "\n", "few_shot_structured_llm = prompt | structured_llm\n", "\n", "# 这个方式也能用，但是不建议\n", "# few_shot_structured_llm.invoke(\"what's something funny about woodpeckers\")\n", "\n", "# 推荐\n", "few_shot_structured_llm.invoke({\"input\":\"what's something funny about woodpeckers\"})"]}, {"cell_type": "code", "execution_count": 24, "id": "43f74988", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'punchline': 'Crocodile who?',\n", " 'setup': \"Crocodile-tears won't work on me, I see right past that toothy grin!\"}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# using tool calling in few-shot examples\n", "from langchain_core.messages import AIMessage, HumanMessage, ToolMessage\n", "\n", "examples = [\n", "    HumanMessage(\"Tell me a joke about planes\", name=\"example_user\"),\n", "    AIMessage(\n", "        \"\",\n", "        name=\"example_assistant\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"joke\",\n", "                \"args\": {\n", "                    \"setup\": \"Why don't planes ever get tired?\",\n", "                    \"punchline\": \"Because they have rest wings!\",\n", "                    \"rating\": 2,\n", "                },\n", "                \"id\": \"1\",\n", "            }\n", "        ],\n", "    ),\n", "    ToolMessage(\"\", tool_call_id=\"1\"),\n", "    HumanMessage(\"Tell me anther joke about planes\", name=\"example_user\"),\n", "    AIMessage(\n", "        \"\",\n", "        name=\"example_assistant\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"joke\",\n", "                \"args\": {\n", "                    \"setup\": \"Cargo\",\n", "                    \"punchline\": \"Cargo 'vroom vroom', but planes go 'zoom zoom'!\",\n", "                    \"rating\": 10,\n", "                },\n", "                \"id\": \"2\",\n", "            }\n", "        ],\n", "    ),\n", "    ToolMessage(\"\", tool_call_id=\"2\"),\n", "    HumanMessage(\"Now about caterpillars\", name=\"example_user\"),\n", "    AIMessage(\n", "        \"\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"joke\",\n", "                \"args\": {\n", "                    \"setup\": \"Caterpillar\",\n", "                    \"punchline\": \"<PERSON><PERSON><PERSON><PERSON> really slow, but watch me turn into a butterfly and steal the show!\",\n", "                    \"rating\": 5,\n", "                },\n", "                \"id\": \"3\",\n", "            }\n", "        ],\n", "    ),\n", "    ToolMessage(\"\", tool_call_id=\"3\"),\n", "]\n", "system = \"\"\"You are a hilarious comedian. Your specialty is knock-knock jokes. \\\n", "Return a joke which has the setup (the response to \"Who's there?\") \\\n", "and the final punchline (the response to \"<setup> who?\").\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [(\"system\", system), (\"placeholder\", \"{examples}\"), (\"human\", \"{input}\")]\n", ")\n", "few_shot_structured_llm = prompt | structured_llm\n", "few_shot_structured_llm.invoke({\"input\": \"crocodiles\", \"examples\": examples})\n"]}, {"cell_type": "markdown", "id": "6fa5c12f", "metadata": {}, "source": ["## Specifying the method for structuring outputs"]}, {"cell_type": "code", "execution_count": 26, "id": "e6fa2f09", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'setup': \"Why don't cats play poker in the jungle?\",\n", " 'punchline': 'Too many cheetahs!'}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_llm = llm.with_structured_output(None, method=\"json_mode\")\n", "structured_llm.invoke(\n", "    \"Tell me a joke about cats, respond in JSON with `setup` and `punchline` keys\"\n", ")"]}, {"cell_type": "code", "execution_count": 27, "id": "89fec9a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'setup': 'Why did the cat sit on the computer?',\n", " 'punchline': 'Because it wanted to keep an eye on the mouse!'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_llm.invoke(\n", "    \"Tell me a joke about cats, respond in JSON with `setup` and `punchline` keys\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}