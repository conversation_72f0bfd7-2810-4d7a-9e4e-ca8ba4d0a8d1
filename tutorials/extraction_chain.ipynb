{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ccb62d80", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"..\")\n", "\n", "import tool\n", "\n", "model = tool.get_model()"]}, {"cell_type": "code", "execution_count": 3, "id": "f739b256", "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class Person(BaseModel):\n", "    \"\"\"Infomation about a person.\"\"\"\n", "\n", "    name: Optional[str] = Field(default=None, description=\"The name of the person\")\n", "    hair_color: Optional[str] = Field(\n", "        default=None, description=\"The color of the person's hair if known\"\n", "    )\n", "    height_in_meters: Optional[str] = Field(\n", "        default=None, description=\"Height measured in meters\"\n", "    )\n", "\n", "structured_output_llm = model.with_structured_output(Person)"]}, {"cell_type": "code", "execution_count": 4, "id": "1ab5ac70", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(name='<PERSON>', hair_color='blond', height_in_meters=None)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "prompt_template = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are an expert extraction algorithm. \"\n", "            \"Only extract relevant information from the text. \"\n", "            \"If you do not know the value of an attribute asked to extract, \"\n", "            \"return null for the attribute's value.\",\n", "        ),\n", "        (\"human\", \"{text}\")\n", "    ]\n", ")\n", "\n", "text = \"<PERSON> is 6 feet tall and has blond hair.\"\n", "prompt = prompt_template.invoke({\"text\": text})\n", "structured_output_llm.invoke(prompt)"]}, {"cell_type": "markdown", "id": "ef3f6152", "metadata": {}, "source": ["### Multiple Entities"]}, {"cell_type": "code", "execution_count": 5, "id": "09adf0b5", "metadata": {}, "outputs": [], "source": ["from typing import List, Optional\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class Person(BaseModel):\n", "    \"\"\"Information about a person.\"\"\"\n", "    \n", "    name: Optional[str] = Field(default=None, description=\"The name of the person\")\n", "    hair_color: Optional[str] = Field(\n", "        default=None, description=\"The color of the person's hair if known\"\n", "    )\n", "    height_in_meters: Optional[str] = Field(\n", "        default=None, description=\"Height measured in meters\"\n", "    )\n", "\n", "class Data(BaseModel):\n", "    people: List[Person]"]}, {"cell_type": "code", "execution_count": 9, "id": "9a6ae29c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('people', [Person(name='<PERSON>', hair_color='black', height_in_meters='1.8288'), Person(name='<PERSON>', hair_color='black', height_in_meters=None)])\n", "\n"]}], "source": ["structured_output_llm = model.with_structured_output(Data)\n", "text = \"My name is <PERSON>, my hair is black and i am 6 feet tall. <PERSON> has the same color hair as me.\"\n", "prompt = prompt_template.invoke({\"text\": text})\n", "\n", "for i in structured_output_llm.invoke(prompt):\n", "    print(f\"{i}\\n\")"]}, {"cell_type": "markdown", "id": "d8838ba1", "metadata": {}, "source": ["### Reference examples"]}, {"cell_type": "code", "execution_count": 11, "id": "1cd0753e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7  \n", "\n", "(The pattern appears to be: **a 🦜 b = a + b**)  \n", "\n", "So:  \n", "- 3 🦜 4 = 3 + 4 = **7**  \n", "- For any numbers *x* and *y*, *x* 🦜 *y* = *x* + *y*.  \n", "\n", "Let me know if you'd like to explore a different pattern! 😊\n"]}], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": \"2 🦜 2\"},\n", "    {\"role\": \"assistant\", \"content\": \"4\"},\n", "    {\"role\": \"user\", \"content\": \"2 🦜 3\"},\n", "    {\"role\": \"assistant\", \"content\": \"5\"},\n", "    {\"role\": \"user\", \"content\": \"3 🦜 4\"},\n", "]\n", "\n", "response = model.invoke(messages)\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": 15, "id": "33591c6d", "metadata": {}, "outputs": [], "source": ["from langchain_core.utils.function_calling import tool_example_to_messages\n", "\n", "examples = [\n", "    (\n", "        \"The ocean is vast and blue. It's more than 20,000 feet deep.\",\n", "        Data(people=[]),\n", "    ),\n", "    (\n", "        \"<PERSON> traveled far from France to Spain.\",\n", "        Data(people=[Person(name=\"<PERSON>\", height_in_meters=None, hair_color=None)]),\n", "    ),\n", "]\n", "\n", "messages = []\n", "\n", "for txt, tool_call in examples:\n", "    if tool_call.people:\n", "        ai_response = \"Detected people.\"\n", "    else:\n", "        ai_response = \"Detected no people.\"\n", "    messages.extend(tool_example_to_messages(txt, [tool_call], ai_response=ai_response))"]}, {"cell_type": "code", "execution_count": 18, "id": "3e837903", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "The ocean is vast and blue. It's more than 20,000 feet deep.\n", "None\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Data (865d5155-f4e7-4ec0-99a8-22302eb0573f)\n", " Call ID: 865d5155-f4e7-4ec0-99a8-22302eb0573f\n", "  Args:\n", "    people: []\n", "None\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "You have correctly called this tool.\n", "None\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Detected no people.\n", "None\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "<PERSON> traveled far from France to Spain.\n", "None\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Data (dc6a507c-d07e-4840-a13e-75c6559bd2d5)\n", " Call ID: dc6a507c-d07e-4840-a13e-75c6559bd2d5\n", "  Args:\n", "    people: [{'name': '<PERSON>', 'hair_color': None, 'height_in_meters': None}]\n", "None\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "You have correctly called this tool.\n", "None\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Detected people.\n", "None\n"]}], "source": ["for message in messages:\n", "    print(message.pretty_print())"]}, {"cell_type": "code", "execution_count": 20, "id": "28538dad", "metadata": {}, "outputs": [{"data": {"text/plain": ["Data(people=[Person(name='<PERSON>', hair_color='brown', height_in_meters='1.75'), Person(name='<PERSON>', hair_color='black', height_in_meters='1.68')])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# with no messages.\n", "message_no_extraction = {\n", "    \"role\": \"user\",\n", "    \"content\": \"The solar system is large, but earth has only 1 moon.\",\n", "}\n", "structured_output_llm = model.with_structured_output(Data)\n", "structured_output_llm.invoke([message_no_extraction])"]}, {"cell_type": "code", "execution_count": 23, "id": "108d701d", "metadata": {}, "outputs": [{"data": {"text/plain": ["Data(people=[Person(name=None, hair_color=None, height_in_meters=None)])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_output_llm.invoke(messages + [message_no_extraction])"]}, {"cell_type": "code", "execution_count": null, "id": "1b433c5c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}