from typing import Optional
from pydantic import BaseModel, Field
from langchain_core.tools import tool as langchain_tool, StructuredTool, BaseTool
from langchain_core.tools.base import ArgsSchema
from langchain_core.callbacks import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.messages import HumanMessage

import tool


class TodoAddParams:
    content: str = Field(description="The content of the todo item")
    start_at: int | None = Field(
        None, description="The started time of the todo item, in unix timestamp"
    )
    deadline: int | None = Field(
        None, description="The deadline of the todo item, in unix timestamp"
    )


@langchain_tool
def add_tool(params: TodoAddParams) -> str:
    """Add a todo item"""
    return "add todo success"


# update tool
class TodoUpdateParams:
    id: str = Field(description="id of the todo")
    content: str | None = Field(default=None, description="content of the todo")
    start_at: int | None = Field(
        default=None, description="start time in unix timestamp"
    )
    deadline: int | None = Field(
        default=None, description="deadline of the todo in unix timestamp"
    )
    done: bool | None = Field(default=None, description="done status")


def update_to_func(params: TodoUpdateParams) -> str:
    """Update a todo item, eg: content,deadline..."""
    return "update todo sucdess"


update_tool = StructuredTool.from_function(name="update_todo", func=update_to_func)


# 	return `{"todos": [{"id": "1", "content": "在2024年12月10日之前完成Eino项目演示文稿的准备工作", "started_at": 1717401600, "deadline": 1717488000, "done": false}]}`, nil


class TodoListInput(BaseModel):
    finished: bool | None = Field(
        default=None, description="filter todo items if finished"
    )


class TodoListTool(BaseTool):
    name: str = "list_todo"
    description: str = "List all todo items"
    args_schema: Optional[ArgsSchema] = TodoAddParams
    return_direct: bool = True

    def _run(
        self, finished: bool, run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        return """{"todos": [{"id": "1", "content": "在2024年12月10日之前完成Eino项目演示文稿的准备工作", "started_at": 1717401600, "deadline": 1717488000, "done": false}]}"""

    async def _arun(
        self,
        finished: bool,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> str:
        return self._run(finished, run_manager=run_manager.get_sync())


todo_list_tool = TodoListTool()

print(type(add_tool), type(update_tool), type(todo_list_tool))

search_tool = DuckDuckGoSearchRun()


llm = tool.get_model()
llm_with_tools = llm.bind_tools([add_tool, update_tool, todo_list_tool, search_tool])
llm_with_tools.invoke(Hu)
