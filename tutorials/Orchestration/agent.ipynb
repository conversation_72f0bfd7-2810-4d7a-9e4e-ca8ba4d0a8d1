{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3691a654", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../..\")\n", "\n", "import tool\n", "\n", "\n", "model = tool.get_model()"]}, {"cell_type": "code", "execution_count": null, "id": "9422671d", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langchain_tavily import TavilySearch\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "memory = MemorySaver()\n", "search = TavilySearch(max_results=2)\n", "tools = [search]\n", "\n", "agent_executor = create_react_agent(model, tools, checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 5, "id": "d4b546e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hi, I'm <PERSON> and I life in SF.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "It sounds like you're really embracing life in San Francisco, Bob! If you ever need tips on the best cafes, hiking spots, or hidden gems in SF—or anything else—feel free to ask. I'm here to help! 🌉\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"abc123\"}}\n", "\n", "input_message = {\n", "    \"role\": \"user\",\n", "    \"content\": \"<PERSON>, I'm <PERSON> and I life in SF.\",\n", "}\n", "\n", "for step in agent_executor.stream(\n", "    {\"messages\": [input_message]}, config=config, stream_mode=\"values\"\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "52e3f5e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What's the weather where I live?\n"]}, {"ename": "AttributeError", "evalue": "'AddableValuesDict' object has no attribute 'text'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[47]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m step \u001b[38;5;129;01min\u001b[39;00m agent_executor.stream(\n\u001b[32m      7\u001b[39m     {\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: [input_message]}, config, stream_mode=\u001b[33m\"\u001b[39m\u001b[33mvalues\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      8\u001b[39m ):\n\u001b[32m      9\u001b[39m     step[\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m][-\u001b[32m1\u001b[39m].pretty_print()\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m     \u001b[43mstep\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtext\u001b[49m()\n", "\u001b[31mAttributeError\u001b[39m: 'AddableValuesDict' object has no attribute 'text'"]}], "source": ["input_message = {\n", "    \"role\": \"user\",\n", "    \"content\": \"What's the weather where I live?\",\n", "}\n", "\n", "for step in agent_executor.stream(\n", "    {\"messages\": [input_message]}, config, stream_mode=\"values\"\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 8, "id": "e0a6d8b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'query': 'What is the weather in SF?', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'Weather in San Francisco', 'url': 'https://www.weatherapi.com/', 'content': \"{'location': {'name': 'San Francisco', 'region': 'California', 'country': 'United States of America', 'lat': 37.775, 'lon': -122.4183, 'tz_id': 'America/Los_Angeles', 'localtime_epoch': 1751115463, 'localtime': '2025-06-28 05:57'}, 'current': {'last_updated_epoch': 1751114700, 'last_updated': '2025-06-28 05:45', 'temp_c': 12.8, 'temp_f': 55.0, 'is_day': 1, 'condition': {'text': 'Mist', 'icon': '//cdn.weatherapi.com/weather/64x64/day/143.png', 'code': 1030}, 'wind_mph': 2.5, 'wind_kph': 4.0, 'wind_degree': 233, 'wind_dir': 'SW', 'pressure_mb': 1014.0, 'pressure_in': 29.93, 'precip_mm': 0.0, 'precip_in': 0.0, 'humidity': 93, 'cloud': 25, 'feelslike_c': 13.2, 'feelslike_f': 55.8, 'windchill_c': 9.9, 'windchill_f': 49.8, 'heatindex_c': 10.6, 'heatindex_f': 51.2, 'dewpoint_c': 9.8, 'dewpoint_f': 49.7, 'vis_km': 16.0, 'vis_miles': 9.0, 'uv': 0.0, 'gust_mph': 4.7, 'gust_kph': 7.6}}\", 'score': 0.9533526, 'raw_content': None}, {'title': 'Saturday, June 28, 2025. San Francisco, CA - Weather Forecast', 'url': 'https://weathershogun.com/weather/usa/ca/san-francisco/480/june/2025-06-28', 'content': \"Saturday, June 28, 2025. San Francisco, CA - Weather Forecast  San Francisco, CA Image 1: WeatherShogun.com HomeContactBrowse StatesPrivacy PolicyTerms and Conditions °F)°C) TodayTomorrowHourly7 days30 daysJune San Francisco, California Weather:  Saturday, June 28, 2025 Day 64° Night 54° Precipitation 0 % Wind 12 mph UV Index (0 - 11+)11 Sunday *   Hourly *   Today *   Tomorrow *   7 days *   30 days Weather Forecast History Last Year's Weather on This Day (June 28, 2024) ### Day 72° ### Night 52° Please note that while we strive for accuracy, the information provided may not always be correct. Use at your own risk. © Copyright by WeatherShogun.com\", 'score': 0.93131906, 'raw_content': None}], 'response_time': 2.36}\n"]}], "source": ["#### practice\n", "from langchain_tavily import TavilySearch\n", "\n", "search = TavilySearch(max_results=2)\n", "search_results = search.invoke(\"What is the weather in SF?\")\n", "print(search_results)"]}, {"cell_type": "code", "execution_count": 19, "id": "ca04dbd8", "metadata": {}, "outputs": [], "source": ["# define tools\n", "tools = [search]"]}, {"cell_type": "code", "execution_count": 10, "id": "38ba7d44", "metadata": {}, "outputs": [], "source": ["model = tool.get_model()"]}, {"cell_type": "code", "execution_count": 20, "id": "e884a79e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hi there! 😊 How can I assist you today?'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"Hi!\"\n", "response = model.invoke([{\"role\": \"user\", \"content\": query}])\n", "response.text()"]}, {"cell_type": "code", "execution_count": 21, "id": "026fd066", "metadata": {}, "outputs": [], "source": ["model_with_tools = model.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 22, "id": "def86abc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message content: Hello! How can I assist you today? 😊\n", "\n", "Tool calls: []\n"]}], "source": ["query = \"Hi!\"\n", "response = model_with_tools.invoke([{\"role\": \"user\", \"content\": query}])\n", "\n", "print(f\"Message content: {response.text()}\\n\")\n", "print(f\"Tool calls: {response.tool_calls}\")"]}, {"cell_type": "code", "execution_count": 45, "id": "3f87badb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message content: \n", "\n", "Tool calls: [{'name': 'tavily_search', 'args': {'query': 'current weather in San Francisco', 'include_images': False, 'search_depth': 'basic'}, 'id': '0197b6d7760975c3debe209dc07d867d', 'type': 'tool_call'}]\n"]}], "source": ["query = \"Search for the weather in SF\"\n", "response = model_with_tools.invoke([{\"role\": \"user\", \"content\": query}])\n", "\n", "print(f\"Message content: {response.text()}\\n\")\n", "print(f\"Tool calls: {response.tool_calls}\")"]}, {"cell_type": "code", "execution_count": 46, "id": "9fb237f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["ToolMessage(content='{\"query\": \"current weather in San Francisco\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"title\": \"Weather in San Francisco\", \"url\": \"https://www.weatherapi.com/\", \"content\": \"{\\'location\\': {\\'name\\': \\'San Francisco\\', \\'region\\': \\'California\\', \\'country\\': \\'United States of America\\', \\'lat\\': 37.775, \\'lon\\': -122.4183, \\'tz_id\\': \\'America/Los_Angeles\\', \\'localtime_epoch\\': 1751119326, \\'localtime\\': \\'2025-06-28 07:02\\'}, \\'current\\': {\\'last_updated_epoch\\': 1751119200, \\'last_updated\\': \\'2025-06-28 07:00\\', \\'temp_c\\': 12.2, \\'temp_f\\': 54.0, \\'is_day\\': 1, \\'condition\\': {\\'text\\': \\'Mist\\', \\'icon\\': \\'//cdn.weatherapi.com/weather/64x64/day/143.png\\', \\'code\\': 1030}, \\'wind_mph\\': 2.5, \\'wind_kph\\': 4.0, \\'wind_degree\\': 228, \\'wind_dir\\': \\'SW\\', \\'pressure_mb\\': 1014.0, \\'pressure_in\\': 29.95, \\'precip_mm\\': 0.0, \\'precip_in\\': 0.0, \\'humidity\\': 93, \\'cloud\\': 25, \\'feelslike_c\\': 12.6, \\'feelslike_f\\': 54.6, \\'windchill_c\\': 10.1, \\'windchill_f\\': 50.2, \\'heatindex_c\\': 10.8, \\'heatindex_f\\': 51.4, \\'dewpoint_c\\': 10.5, \\'dewpoint_f\\': 50.9, \\'vis_km\\': 16.0, \\'vis_miles\\': 9.0, \\'uv\\': 0.3, \\'gust_mph\\': 4.5, \\'gust_kph\\': 7.2}}\", \"score\": 0.9049154, \"raw_content\": null}, {\"title\": \"Saturday, June 28, 2025. San Francisco, CA - Weather Forecast\", \"url\": \"https://weathershogun.com/weather/usa/ca/san-francisco/480/june/2025-06-28\", \"content\": \"San Francisco, California weather forecast for Saturday, June 28, 2025. Get the latest on temperature, precipitation, wind speed, and UV. Plan your day with accurate weather updates.\", \"score\": 0.8773195, \"raw_content\": null}], \"response_time\": 1.99}', name='tavily_search', tool_call_id='0197b6d7760975c3debe209dc07d867d')"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["model_generated_tool_call = response.tool_calls[0]\n", "search.invoke(model_generated_tool_call)"]}, {"cell_type": "code", "execution_count": 24, "id": "3fb8f35a", "metadata": {}, "outputs": [], "source": ["# create the agent.\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "agent_executor = create_react_agent(model, tools)"]}, {"cell_type": "code", "execution_count": 25, "id": "3750c91f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hi!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello! How can I assist you today? 😊\n"]}], "source": ["# run the agent\n", "input_message = {\"role\": \"user\", \"content\": \"Hi!\"}\n", "response = agent_executor.invoke({\"messages\": [input_message]})\n", "\n", "for message in response[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "code", "execution_count": 26, "id": "686a6efb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Search for the weather in SF?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  tavily_search (0197b6c48bde6bb9b6fe6b9294e3375f)\n", " Call ID: 0197b6c48bde6bb9b6fe6b9294e3375f\n", "  Args:\n", "    query: current weather in San Francisco\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"current weather in San Francisco\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"title\": \"Weather in San Francisco\", \"url\": \"https://www.weatherapi.com/\", \"content\": \"{'location': {'name': 'San Francisco', 'region': 'California', 'country': 'United States of America', 'lat': 37.775, 'lon': -122.4183, 'tz_id': 'America/Los_Angeles', 'localtime_epoch': 1751117239, 'localtime': '2025-06-28 06:27'}, 'current': {'last_updated_epoch': 1751116500, 'last_updated': '2025-06-28 06:15', 'temp_c': 12.2, 'temp_f': 54.0, 'is_day': 1, 'condition': {'text': 'Mist', 'icon': '//cdn.weatherapi.com/weather/64x64/day/143.png', 'code': 1030}, 'wind_mph': 2.2, 'wind_kph': 3.6, 'wind_degree': 243, 'wind_dir': 'WSW', 'pressure_mb': 1014.0, 'pressure_in': 29.95, 'precip_mm': 0.0, 'precip_in': 0.0, 'humidity': 93, 'cloud': 25, 'feelslike_c': 12.7, 'feelslike_f': 54.8, 'windchill_c': 10.1, 'windchill_f': 50.2, 'heatindex_c': 10.8, 'heatindex_f': 51.5, 'dewpoint_c': 10.4, 'dewpoint_f': 50.8, 'vis_km': 16.0, 'vis_miles': 9.0, 'uv': 0.0, 'gust_mph': 4.4, 'gust_kph': 7.1}}\", \"score\": 0.9450423, \"raw_content\": null}, {\"title\": \"Saturday, June 28, 2025. San Francisco, CA - Weather Forecast\", \"url\": \"https://weathershogun.com/weather/usa/ca/san-francisco/480/june/2025-06-28\", \"content\": \"San Francisco, California weather forecast for Saturday, June 28, 2025. Get the latest on temperature, precipitation, wind speed, and UV. Plan your day with accurate weather updates.\", \"score\": 0.8773195, \"raw_content\": null}], \"response_time\": 2.01}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The current weather in San Francisco is as follows:\n", "\n", "- **Temperature:** 12.2°C (54.0°F)\n", "- **Condition:** Mist\n", "- **Wind:** 3.6 kph (2.2 mph) from WSW\n", "- **Humidity:** 93%\n", "- **Visibility:** 16 km (9 miles)\n", "- **Pressure:** 1014.0 mb (29.95 in)\n", "\n", "For more details, you can check [Weather API](https://www.weatherapi.com/) or [Weather Shogun](https://weathershogun.com/weather/usa/ca/san-francisco/480/june/2025-06-28).\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"Search for the weather in SF?\"}\n", "response = agent_executor.invoke({\"messages\": [input_message]})\n", "\n", "for message in response[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "code", "execution_count": 28, "id": "644d6d66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Search for the weather in Zhengzhou?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"Search for the weather in Zhengzhou?\"}\n", "response = agent_executor.invoke({\"messages\": [input_message]})\n", "\n", "for message in response[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "98b4e7c2", "metadata": {}, "source": ["### Streaming messages"]}, {"cell_type": "code", "execution_count": 32, "id": "4fb16c70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Search for the weather in SF?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"Search for the weather in SF?\"}\n", "for step in agent_executor.stream({\"messages\": [input_message]}, stream_mode=\"values\"):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 37, "id": "e67030af", "metadata": {}, "outputs": [], "source": ["for step, metadata in agent_executor.stream(\n", "    {\"messages\": [input_message]}, stream_mode=\"messaegs\"\n", "):\n", "    if metadata[\"langgraph_node\"] == \"agent\" and (text := step.text()):\n", "        print(text, end=\"|\")"]}, {"cell_type": "markdown", "id": "a64be3a9", "metadata": {}, "source": ["### Adding in memory"]}, {"cell_type": "code", "execution_count": 38, "id": "5c64e7f7", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "\n", "memory = MemorySaver()"]}, {"cell_type": "code", "execution_count": 39, "id": "c2736920", "metadata": {}, "outputs": [], "source": ["agent_executor = create_react_agent(model, tools, checkpointer=memory)\n", "\n", "config = {\"configurable\": {\"thread_id\": \"abc123\"}}"]}, {"cell_type": "code", "execution_count": 40, "id": "85c6baf8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hi, I'm <PERSON>!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi, <PERSON>! How can I assist you today?\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"<PERSON>, I'm <PERSON>!\"}\n", "for step in agent_executor.stream(\n", "    {\"messages\": [input_message]}, config, stream_mode=\"values\"\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 41, "id": "a5657af3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What's my name?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Your name is <PERSON>! How can I help you, <PERSON>? 😊\n"]}], "source": ["input_message = {\"role\": \"user\", \"content\": \"What's my name?\"}\n", "for step in agent_executor.stream(\n", "    {\"messages\": [input_message]}, config, stream_mode=\"values\"\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 42, "id": "195003f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What's my name?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I don't have access to personal information, including your name, unless you share it with me. How would you like me to address you?\n"]}], "source": ["# start a new conversation\n", "config = {\"configurable\": {\"thread_id\": \"xyz123\"}}\n", "\n", "input_message = {\"role\": \"user\", \"content\": \"What's my name?\"}\n", "for step in agent_executor.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "de589272", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "learn-langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}